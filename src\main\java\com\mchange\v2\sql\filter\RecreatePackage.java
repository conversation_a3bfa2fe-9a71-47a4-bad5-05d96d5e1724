package com.mchange.v2.sql.filter;

import com.mchange.v1.lang.ClassUtils;
import com.mchange.v2.codegen.intfc.DelegatorGenerator;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import javax.sql.DataSource;

/* loaded from: mchange-commons-java-0.2.11.jar:com/mchange/v2/sql/filter/RecreatePackage.class */
public final class RecreatePackage {
    static final Class[] intfcs = {Connection.class, ResultSet.class, DatabaseMetaData.class, Statement.class, PreparedStatement.class, CallableStatement.class, DataSource.class};

    public static void main(String[] strArr) {
        BufferedWriter bufferedWriter;
        try {
            DelegatorGenerator delegatorGenerator = new DelegatorGenerator();
            String name = RecreatePackage.class.getName();
            String substring = name.substring(0, name.lastIndexOf(46));
            for (int i = 0; i < intfcs.length; i++) {
                Class cls = intfcs[i];
                String simpleClassName = ClassUtils.simpleClassName(cls);
                String str = "Filter" + simpleClassName;
                String str2 = "SynchronizedFilter" + simpleClassName;
                BufferedWriter bufferedWriter2 = null;
                try {
                    bufferedWriter2 = new BufferedWriter(new FileWriter(str + ".java"));
                    delegatorGenerator.setMethodModifiers(1);
                    delegatorGenerator.writeDelegator(cls, substring + '.' + str, bufferedWriter2);
                    System.err.println(str);
                    if (bufferedWriter2 != null) {
                        try {
                            bufferedWriter2.close();
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    try {
                        bufferedWriter2 = new BufferedWriter(new FileWriter(str2 + ".java"));
                        delegatorGenerator.setMethodModifiers(33);
                        delegatorGenerator.writeDelegator(cls, substring + '.' + str2, bufferedWriter2);
                        System.err.println(str2);
                        if (bufferedWriter2 != null) {
                            try {
                                bufferedWriter2.close();
                            } catch (Exception e2) {
                                e2.printStackTrace();
                            }
                        }
                    } finally {
                        if (bufferedWriter != null) {
                            try {
                            } catch (Exception e3) {
                            }
                        }
                    }
                } finally {
                    if (bufferedWriter != null) {
                        try {
                        } catch (Exception e32) {
                        }
                    }
                }
            }
        } catch (Exception e4) {
            e4.printStackTrace();
        }
    }

    private RecreatePackage() {
    }
}
