package aj.org.objectweb.asm.signature;

/* loaded from: aspectjweaver-1.8.0.jar:aj/org/objectweb/asm/signature/SignatureReader.class */
public class SignatureReader {
    private final String a;

    public SignatureReader(String str) {
        this.a = str;
    }

    public void accept(SignatureVisitor signatureVisitor) {
        int i;
        char charAt;
        String str = this.a;
        int length = str.length();
        if (str.charAt(0) == '<') {
            i = 2;
            do {
                int indexOf = str.indexOf(58, i);
                signatureVisitor.visitFormalTypeParameter(str.substring(i - 1, indexOf));
                int i2 = indexOf + 1;
                char charAt2 = str.charAt(i2);
                if (charAt2 == 'L' || charAt2 == '[' || charAt2 == 'T') {
                    i2 = a(str, i2, signatureVisitor.visitClassBound());
                }
                while (true) {
                    int i3 = i2;
                    i = i2 + 1;
                    charAt = str.charAt(i3);
                    if (charAt != ':') {
                        break;
                    } else {
                        i2 = a(str, i, signatureVisitor.visitInterfaceBound());
                    }
                }
            } while (charAt != '>');
        } else {
            i = 0;
        }
        if (str.charAt(i) == '(') {
            int i4 = i + 1;
            while (str.charAt(i4) != ')') {
                i4 = a(str, i4, signatureVisitor.visitParameterType());
            }
            int a = a(str, i4 + 1, signatureVisitor.visitReturnType());
            while (true) {
                int i5 = a;
                if (i5 >= length) {
                    return;
                } else {
                    a = a(str, i5 + 1, signatureVisitor.visitExceptionType());
                }
            }
        } else {
            int a2 = a(str, i, signatureVisitor.visitSuperclass());
            while (true) {
                int i6 = a2;
                if (i6 >= length) {
                    return;
                } else {
                    a2 = a(str, i6, signatureVisitor.visitInterface());
                }
            }
        }
    }

    public void acceptType(SignatureVisitor signatureVisitor) {
        a(this.a, 0, signatureVisitor);
    }

    /* JADX WARN: Failed to find 'out' block for switch in B:34:0x0145. Please report as an issue. */
    private static int a(String str, int i, SignatureVisitor signatureVisitor) {
        int i2 = i + 1;
        char charAt = str.charAt(i);
        switch (charAt) {
            case 'B':
            case 'C':
            case 'D':
            case 'F':
            case 'I':
            case 'J':
            case 'S':
            case 'V':
            case 'Z':
                signatureVisitor.visitBaseType(charAt);
                return i2;
            case 'E':
            case 'G':
            case 'H':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'U':
            case 'W':
            case 'X':
            case 'Y':
            default:
                int i3 = i2;
                boolean z = false;
                boolean z2 = false;
                while (true) {
                    int i4 = i2;
                    i2++;
                    char charAt2 = str.charAt(i4);
                    switch (charAt2) {
                        case '.':
                        case ';':
                            if (!z) {
                                String substring = str.substring(i3, i2 - 1);
                                if (z2) {
                                    signatureVisitor.visitInnerClassType(substring);
                                } else {
                                    signatureVisitor.visitClassType(substring);
                                }
                            }
                            if (charAt2 != ';') {
                                i3 = i2;
                                z = false;
                                z2 = true;
                                break;
                            } else {
                                signatureVisitor.visitEnd();
                                return i2;
                            }
                        case '<':
                            String substring2 = str.substring(i3, i2 - 1);
                            if (z2) {
                                signatureVisitor.visitInnerClassType(substring2);
                            } else {
                                signatureVisitor.visitClassType(substring2);
                            }
                            z = true;
                            while (true) {
                                char charAt3 = str.charAt(i2);
                                switch (charAt3) {
                                    case '*':
                                        i2++;
                                        signatureVisitor.visitTypeArgument();
                                    case '+':
                                    case '-':
                                        i2 = a(str, i2 + 1, signatureVisitor.visitTypeArgument(charAt3));
                                    case '>':
                                        break;
                                    default:
                                        i2 = a(str, i2, signatureVisitor.visitTypeArgument('='));
                                }
                            }
                            break;
                    }
                }
                break;
            case 'T':
                int indexOf = str.indexOf(59, i2);
                signatureVisitor.visitTypeVariable(str.substring(i2, indexOf));
                return indexOf + 1;
            case '[':
                return a(str, i2, signatureVisitor.visitArrayType());
        }
    }
}
