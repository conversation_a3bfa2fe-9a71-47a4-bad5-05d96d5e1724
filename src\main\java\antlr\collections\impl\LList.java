package antlr.collections.impl;

import antlr.collections.List;
import antlr.collections.Stack;
import java.util.Enumeration;
import java.util.NoSuchElementException;

/* loaded from: antlr-2.7.7.jar:antlr/collections/impl/LList.class */
public class LList implements List, Stack {
    protected LLCell head = null;
    protected LLCell tail = null;
    protected int length = 0;

    @Override // antlr.collections.List
    public void add(Object obj) {
        append(obj);
    }

    @Override // antlr.collections.List
    public void append(Object obj) {
        LLCell lLCell = new LLCell(obj);
        if (this.length == 0) {
            this.tail = lLCell;
            this.head = lLCell;
            this.length = 1;
        } else {
            this.tail.next = lLCell;
            this.tail = lLCell;
            this.length++;
        }
    }

    protected Object deleteHead() throws NoSuchElementException {
        if (this.head == null) {
            throw new NoSuchElementException();
        }
        Object obj = this.head.data;
        this.head = this.head.next;
        this.length--;
        return obj;
    }

    @Override // antlr.collections.List
    public Object elementAt(int i) throws NoSuchElementException {
        int i2 = 0;
        LLCell lLCell = this.head;
        while (true) {
            LLCell lLCell2 = lLCell;
            if (lLCell2 != null) {
                if (i == i2) {
                    return lLCell2.data;
                }
                i2++;
                lLCell = lLCell2.next;
            } else {
                throw new NoSuchElementException();
            }
        }
    }

    @Override // antlr.collections.List
    public Enumeration elements() {
        return new LLEnumeration(this);
    }

    @Override // antlr.collections.Stack
    public int height() {
        return this.length;
    }

    @Override // antlr.collections.List
    public boolean includes(Object obj) {
        LLCell lLCell = this.head;
        while (true) {
            LLCell lLCell2 = lLCell;
            if (lLCell2 != null) {
                if (lLCell2.data.equals(obj)) {
                    return true;
                }
                lLCell = lLCell2.next;
            } else {
                return false;
            }
        }
    }

    protected void insertHead(Object obj) {
        LLCell lLCell = this.head;
        this.head = new LLCell(obj);
        this.head.next = lLCell;
        this.length++;
        if (this.tail == null) {
            this.tail = this.head;
        }
    }

    @Override // antlr.collections.List
    public int length() {
        return this.length;
    }

    @Override // antlr.collections.Stack
    public Object pop() throws NoSuchElementException {
        return deleteHead();
    }

    @Override // antlr.collections.Stack
    public void push(Object obj) {
        insertHead(obj);
    }

    @Override // antlr.collections.Stack
    public Object top() throws NoSuchElementException {
        if (this.head == null) {
            throw new NoSuchElementException();
        }
        return this.head.data;
    }
}
