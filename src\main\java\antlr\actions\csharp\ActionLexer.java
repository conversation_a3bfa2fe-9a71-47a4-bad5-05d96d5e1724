package antlr.actions.csharp;

import antlr.ActionTransInfo;
import antlr.ByteBuffer;
import antlr.Char<PERSON>uffer;
import antlr.Char<PERSON>canner;
import antlr.CharStreamException;
import antlr.CharStreamIOException;
import antlr.CodeGenerator;
import antlr.InputBuffer;
import antlr.LexerSharedInputState;
import antlr.NoViableAltForCharException;
import antlr.RecognitionException;
import antlr.RuleBlock;
import antlr.Token;
import antlr.TokenStream;
import antlr.TokenStreamException;
import antlr.TokenStreamIOException;
import antlr.TokenStreamRecognitionException;
import antlr.Tool;
import antlr.collections.impl.BitSet;
import antlr.collections.impl.Vector;
import java.io.InputStream;
import java.io.Reader;
import java.io.StringReader;
import java.util.Hashtable;
import org.springframework.jdbc.datasource.init.ScriptUtils;

/* loaded from: antlr-2.7.7.jar:antlr/actions/csharp/ActionLexer.class */
public class ActionLexer extends CharScanner implements ActionLexerTokenTypes, TokenStream {
    protected RuleBlock currentRule;
    protected CodeGenerator generator;
    protected int lineOffset;
    private Tool antlrTool;
    ActionTransInfo transInfo;
    public static final BitSet _tokenSet_0 = new BitSet(mk_tokenSet_0());
    public static final BitSet _tokenSet_1 = new BitSet(mk_tokenSet_1());
    public static final BitSet _tokenSet_2 = new BitSet(mk_tokenSet_2());
    public static final BitSet _tokenSet_3 = new BitSet(mk_tokenSet_3());
    public static final BitSet _tokenSet_4 = new BitSet(mk_tokenSet_4());
    public static final BitSet _tokenSet_5 = new BitSet(mk_tokenSet_5());
    public static final BitSet _tokenSet_6 = new BitSet(mk_tokenSet_6());
    public static final BitSet _tokenSet_7 = new BitSet(mk_tokenSet_7());
    public static final BitSet _tokenSet_8 = new BitSet(mk_tokenSet_8());
    public static final BitSet _tokenSet_9 = new BitSet(mk_tokenSet_9());
    public static final BitSet _tokenSet_10 = new BitSet(mk_tokenSet_10());
    public static final BitSet _tokenSet_11 = new BitSet(mk_tokenSet_11());
    public static final BitSet _tokenSet_12 = new BitSet(mk_tokenSet_12());
    public static final BitSet _tokenSet_13 = new BitSet(mk_tokenSet_13());
    public static final BitSet _tokenSet_14 = new BitSet(mk_tokenSet_14());
    public static final BitSet _tokenSet_15 = new BitSet(mk_tokenSet_15());
    public static final BitSet _tokenSet_16 = new BitSet(mk_tokenSet_16());
    public static final BitSet _tokenSet_17 = new BitSet(mk_tokenSet_17());
    public static final BitSet _tokenSet_18 = new BitSet(mk_tokenSet_18());
    public static final BitSet _tokenSet_19 = new BitSet(mk_tokenSet_19());
    public static final BitSet _tokenSet_20 = new BitSet(mk_tokenSet_20());
    public static final BitSet _tokenSet_21 = new BitSet(mk_tokenSet_21());
    public static final BitSet _tokenSet_22 = new BitSet(mk_tokenSet_22());
    public static final BitSet _tokenSet_23 = new BitSet(mk_tokenSet_23());
    public static final BitSet _tokenSet_24 = new BitSet(mk_tokenSet_24());
    public static final BitSet _tokenSet_25 = new BitSet(mk_tokenSet_25());
    public static final BitSet _tokenSet_26 = new BitSet(mk_tokenSet_26());

    public ActionLexer(String str, RuleBlock ruleBlock, CodeGenerator codeGenerator, ActionTransInfo actionTransInfo) {
        this(new StringReader(str));
        this.currentRule = ruleBlock;
        this.generator = codeGenerator;
        this.transInfo = actionTransInfo;
    }

    public void setLineOffset(int i) {
        setLine(i);
    }

    public void setTool(Tool tool) {
        this.antlrTool = tool;
    }

    @Override // antlr.CharScanner
    public void reportError(RecognitionException recognitionException) {
        this.antlrTool.error(new StringBuffer().append("Syntax error in action: ").append((Object) recognitionException).toString(), getFilename(), getLine(), getColumn());
    }

    @Override // antlr.CharScanner
    public void reportError(String str) {
        this.antlrTool.error(str, getFilename(), getLine(), getColumn());
    }

    @Override // antlr.CharScanner
    public void reportWarning(String str) {
        if (getFilename() == null) {
            this.antlrTool.warning(str);
        } else {
            this.antlrTool.warning(str, getFilename(), getLine(), getColumn());
        }
    }

    public ActionLexer(InputStream inputStream) {
        this(new ByteBuffer(inputStream));
    }

    public ActionLexer(Reader reader) {
        this(new CharBuffer(reader));
    }

    public ActionLexer(InputBuffer inputBuffer) {
        this(new LexerSharedInputState(inputBuffer));
    }

    public ActionLexer(LexerSharedInputState lexerSharedInputState) {
        super(lexerSharedInputState);
        this.lineOffset = 0;
        this.caseSensitiveLiterals = true;
        setCaseSensitive(true);
        this.literals = new Hashtable();
    }

    @Override // antlr.TokenStream
    public Token nextToken() throws TokenStreamException {
        do {
            resetText();
            try {
                try {
                    if (LA(1) >= 3 && LA(1) <= 255) {
                        mACTION(true);
                        Token token = this._returnToken;
                    } else {
                        if (LA(1) != 65535) {
                            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                        }
                        uponEOF();
                        this._returnToken = makeToken(1);
                    }
                } catch (RecognitionException e) {
                    throw new TokenStreamRecognitionException(e);
                }
            } catch (CharStreamException e2) {
                if (e2 instanceof CharStreamIOException) {
                    throw new TokenStreamIOException(((CharStreamIOException) e2).io);
                }
                throw new TokenStreamException(e2.getMessage());
            }
        } while (this._returnToken == null);
        this._returnToken.setType(this._returnToken.getType());
        return this._returnToken;
    }

    public final void mACTION(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        int i = 0;
        while (true) {
            switch (LA(1)) {
                case '#':
                    mAST_ITEM(false);
                    break;
                case '$':
                    mTEXT_ITEM(false);
                    break;
                default:
                    if (_tokenSet_0.member(LA(1))) {
                        mSTUFF(false);
                        break;
                    } else {
                        if (i < 1) {
                            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                        }
                        if (z && 0 == 0 && 4 != -1) {
                            token = makeToken(4);
                            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
                        }
                        this._returnToken = token;
                        return;
                    }
            }
            i++;
        }
    }

    protected final void mSTUFF(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        switch (LA(1)) {
            case '\n':
                match('\n');
                newline();
                break;
            case '\"':
                mSTRING(false);
                break;
            case '\'':
                mCHAR(false);
                break;
            default:
                if (LA(1) == '/' && (LA(2) == '*' || LA(2) == '/')) {
                    mCOMMENT(false);
                    break;
                } else if (LA(1) == '\r' && LA(2) == '\n') {
                    match("\r\n");
                    newline();
                    break;
                } else if (LA(1) == '\\' && LA(2) == '#') {
                    match('\\');
                    match('#');
                    this.text.setLength(length);
                    this.text.append("#");
                    break;
                } else if (LA(1) == '/' && _tokenSet_1.member(LA(2))) {
                    match('/');
                    match(_tokenSet_1);
                    break;
                } else if (LA(1) == '\r') {
                    match('\r');
                    newline();
                    break;
                } else if (_tokenSet_2.member(LA(1))) {
                    match(_tokenSet_2);
                    break;
                } else {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
        }
        if (z && 0 == 0 && 5 != -1) {
            token = makeToken(5);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:32:0x0368  */
    /* JADX WARN: Removed duplicated region for block: B:35:0x0377  */
    /* JADX WARN: Removed duplicated region for block: B:36:0x02c8  */
    /* JADX WARN: Removed duplicated region for block: B:58:0x0336  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    protected final void mAST_ITEM(boolean r9) throws antlr.RecognitionException, antlr.CharStreamException, antlr.TokenStreamException {
        /*
            Method dump skipped, instructions count: 1191
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: antlr.actions.csharp.ActionLexer.mAST_ITEM(boolean):void");
    }

    protected final void mTEXT_ITEM(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        Token token2 = null;
        Token token3 = null;
        if (LA(1) == '$' && LA(2) == 'F' && LA(3) == 'O') {
            match("$FOLLOW");
            if (_tokenSet_5.member(LA(1)) && _tokenSet_6.member(LA(2)) && LA(3) >= 3 && LA(3) <= 255) {
                switch (LA(1)) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                        mWS(false);
                    case '(':
                        match('(');
                        mTEXT_ARG(true);
                        token2 = this._returnToken;
                        match(')');
                        break;
                    default:
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
            }
            String ruleName = this.currentRule.getRuleName();
            if (token2 != null) {
                ruleName = token2.getText();
            }
            String fOLLOWBitSet = this.generator.getFOLLOWBitSet(ruleName, 1);
            if (fOLLOWBitSet == null) {
                reportError(new StringBuffer().append("$FOLLOW(").append(ruleName).append(")").append(": unknown rule or bad lookahead computation").toString());
            } else {
                this.text.setLength(length);
                this.text.append(fOLLOWBitSet);
            }
        } else if (LA(1) == '$' && LA(2) == 'F' && LA(3) == 'I') {
            match("$FIRST");
            if (_tokenSet_5.member(LA(1)) && _tokenSet_6.member(LA(2)) && LA(3) >= 3 && LA(3) <= 255) {
                switch (LA(1)) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                        mWS(false);
                    case '(':
                        match('(');
                        mTEXT_ARG(true);
                        token3 = this._returnToken;
                        match(')');
                        break;
                    default:
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
            }
            String ruleName2 = this.currentRule.getRuleName();
            if (token3 != null) {
                ruleName2 = token3.getText();
            }
            String fIRSTBitSet = this.generator.getFIRSTBitSet(ruleName2, 1);
            if (fIRSTBitSet == null) {
                reportError(new StringBuffer().append("$FIRST(").append(ruleName2).append(")").append(": unknown rule or bad lookahead computation").toString());
            } else {
                this.text.setLength(length);
                this.text.append(fIRSTBitSet);
            }
        } else if (LA(1) == '$' && LA(2) == 'a') {
            match("$append");
            switch (LA(1)) {
                case '\t':
                case '\n':
                case '\r':
                case ' ':
                    mWS(false);
                case '(':
                    match('(');
                    mTEXT_ARG(true);
                    Token token4 = this._returnToken;
                    match(')');
                    String stringBuffer = new StringBuffer().append("text.Append(").append(token4.getText()).append(")").toString();
                    this.text.setLength(length);
                    this.text.append(stringBuffer);
                    break;
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            }
        } else if (LA(1) == '$' && LA(2) == 's') {
            match("$set");
            if (LA(1) == 'T' && LA(2) == 'e') {
                match("Text");
                switch (LA(1)) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                        mWS(false);
                    case '(':
                        match('(');
                        mTEXT_ARG(true);
                        Token token5 = this._returnToken;
                        match(')');
                        String stringBuffer2 = new StringBuffer().append("text.Length = _begin; text.Append(").append(token5.getText()).append(")").toString();
                        this.text.setLength(length);
                        this.text.append(stringBuffer2);
                        break;
                    default:
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
            } else if (LA(1) == 'T' && LA(2) == 'o') {
                match("Token");
                switch (LA(1)) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                        mWS(false);
                    case '(':
                        match('(');
                        mTEXT_ARG(true);
                        Token token6 = this._returnToken;
                        match(')');
                        String stringBuffer3 = new StringBuffer().append("_token = ").append(token6.getText()).toString();
                        this.text.setLength(length);
                        this.text.append(stringBuffer3);
                        break;
                    default:
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
            } else if (LA(1) == 'T' && LA(2) == 'y') {
                match("Type");
                switch (LA(1)) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                        mWS(false);
                    case '(':
                        match('(');
                        mTEXT_ARG(true);
                        Token token7 = this._returnToken;
                        match(')');
                        String stringBuffer4 = new StringBuffer().append("_ttype = ").append(token7.getText()).toString();
                        this.text.setLength(length);
                        this.text.append(stringBuffer4);
                        break;
                    default:
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
            } else {
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            }
        } else if (LA(1) == '$' && LA(2) == 'g') {
            match("$getText");
            this.text.setLength(length);
            this.text.append("text.ToString(_begin, text.Length-_begin)");
        } else {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 7 != -1) {
            token = makeToken(7);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mCOMMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        if (LA(1) == '/' && LA(2) == '/') {
            mSL_COMMENT(false);
        } else if (LA(1) == '/' && LA(2) == '*') {
            mML_COMMENT(false);
        } else {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 19 != -1) {
            token = makeToken(19);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mSTRING(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        match('\"');
        while (true) {
            if (LA(1) == '\\') {
                mESC(false);
            } else if (!_tokenSet_7.member(LA(1))) {
                break;
            } else {
                matchNot('\"');
            }
        }
        match('\"');
        if (z && 0 == 0 && 23 != -1) {
            token = makeToken(23);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mCHAR(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        match('\'');
        if (LA(1) == '\\') {
            mESC(false);
        } else if (_tokenSet_8.member(LA(1))) {
            matchNot('\'');
        } else {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        match('\'');
        if (z && 0 == 0 && 22 != -1) {
            token = makeToken(22);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mTREE(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        new StringBuffer();
        Vector vector = new Vector(10);
        int length2 = this.text.length();
        match('(');
        this.text.setLength(length2);
        switch (LA(1)) {
            case '\t':
            case '\n':
            case '\r':
            case ' ':
                int length3 = this.text.length();
                mWS(false);
                this.text.setLength(length3);
                break;
            case 11:
            case '\f':
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case '!':
            case '$':
            case '%':
            case '&':
            case '\'':
            case ')':
            case '*':
            case '+':
            case ',':
            case '-':
            case '.':
            case '/':
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
            case ':':
            case ';':
            case '<':
            case '=':
            case '>':
            case '?':
            case '@':
            case '\\':
            case ']':
            case '^':
            case '`':
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            case '\"':
            case '#':
            case '(':
            case 'A':
            case 'B':
            case 'C':
            case 'D':
            case 'E':
            case 'F':
            case 'G':
            case 'H':
            case 'I':
            case 'J':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'S':
            case 'T':
            case 'U':
            case 'V':
            case 'W':
            case 'X':
            case 'Y':
            case 'Z':
            case '[':
            case '_':
            case 'a':
            case 'b':
            case 'c':
            case 'd':
            case 'e':
            case 'f':
            case 'g':
            case 'h':
            case 'i':
            case 'j':
            case 'k':
            case 'l':
            case 'm':
            case 'n':
            case 'o':
            case 'p':
            case 'q':
            case 'r':
            case 's':
            case 't':
            case 'u':
            case 'v':
            case 'w':
            case 'x':
            case 'y':
            case 'z':
                break;
        }
        int length4 = this.text.length();
        mTREE_ELEMENT(true);
        this.text.setLength(length4);
        vector.appendElement(this.generator.processStringForASTConstructor(this._returnToken.getText()));
        switch (LA(1)) {
            case '\t':
            case '\n':
            case '\r':
            case ' ':
                int length5 = this.text.length();
                mWS(false);
                this.text.setLength(length5);
                break;
            case ')':
            case ',':
                break;
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        while (LA(1) == ',') {
            int length6 = this.text.length();
            match(',');
            this.text.setLength(length6);
            switch (LA(1)) {
                case '\t':
                case '\n':
                case '\r':
                case ' ':
                    int length7 = this.text.length();
                    mWS(false);
                    this.text.setLength(length7);
                    break;
                case 11:
                case '\f':
                case 14:
                case 15:
                case 16:
                case 17:
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case '!':
                case '$':
                case '%':
                case '&':
                case '\'':
                case ')':
                case '*':
                case '+':
                case ',':
                case '-':
                case '.':
                case '/':
                case '0':
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                case ':':
                case ';':
                case '<':
                case '=':
                case '>':
                case '?':
                case '@':
                case '\\':
                case ']':
                case '^':
                case '`':
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                case '\"':
                case '#':
                case '(':
                case 'A':
                case 'B':
                case 'C':
                case 'D':
                case 'E':
                case 'F':
                case 'G':
                case 'H':
                case 'I':
                case 'J':
                case 'K':
                case 'L':
                case 'M':
                case 'N':
                case 'O':
                case 'P':
                case 'Q':
                case 'R':
                case 'S':
                case 'T':
                case 'U':
                case 'V':
                case 'W':
                case 'X':
                case 'Y':
                case 'Z':
                case '[':
                case '_':
                case 'a':
                case 'b':
                case 'c':
                case 'd':
                case 'e':
                case 'f':
                case 'g':
                case 'h':
                case 'i':
                case 'j':
                case 'k':
                case 'l':
                case 'm':
                case 'n':
                case 'o':
                case 'p':
                case 'q':
                case 'r':
                case 's':
                case 't':
                case 'u':
                case 'v':
                case 'w':
                case 'x':
                case 'y':
                case 'z':
                    break;
            }
            int length8 = this.text.length();
            mTREE_ELEMENT(true);
            this.text.setLength(length8);
            vector.appendElement(this.generator.processStringForASTConstructor(this._returnToken.getText()));
            switch (LA(1)) {
                case '\t':
                case '\n':
                case '\r':
                case ' ':
                    int length9 = this.text.length();
                    mWS(false);
                    this.text.setLength(length9);
                    break;
                case ')':
                case ',':
                    break;
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            }
        }
        this.text.setLength(length);
        this.text.append(this.generator.getASTCreateString(vector));
        int length10 = this.text.length();
        match(')');
        this.text.setLength(length10);
        if (z && 0 == 0 && 8 != -1) {
            token = makeToken(8);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mWS(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        int i = 0;
        while (true) {
            if (LA(1) == '\r' && LA(2) == '\n') {
                match('\r');
                match('\n');
                newline();
            } else if (LA(1) == ' ') {
                match(' ');
            } else if (LA(1) == '\t') {
                match('\t');
            } else if (LA(1) == '\r') {
                match('\r');
                newline();
            } else {
                if (LA(1) != '\n') {
                    break;
                }
                match('\n');
                newline();
            }
            i++;
        }
        if (i < 1) {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 28 != -1) {
            token = makeToken(28);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mID(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        switch (LA(1)) {
            case 'A':
            case 'B':
            case 'C':
            case 'D':
            case 'E':
            case 'F':
            case 'G':
            case 'H':
            case 'I':
            case 'J':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'S':
            case 'T':
            case 'U':
            case 'V':
            case 'W':
            case 'X':
            case 'Y':
            case 'Z':
                matchRange('A', 'Z');
                break;
            case '[':
            case '\\':
            case ']':
            case '^':
            case '`':
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            case '_':
                match('_');
                break;
            case 'a':
            case 'b':
            case 'c':
            case 'd':
            case 'e':
            case 'f':
            case 'g':
            case 'h':
            case 'i':
            case 'j':
            case 'k':
            case 'l':
            case 'm':
            case 'n':
            case 'o':
            case 'p':
            case 'q':
            case 'r':
            case 's':
            case 't':
            case 'u':
            case 'v':
            case 'w':
            case 'x':
            case 'y':
            case 'z':
                matchRange('a', 'z');
                break;
        }
        while (_tokenSet_9.member(LA(1))) {
            switch (LA(1)) {
                case '0':
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                    matchRange('0', '9');
                    break;
                case ':':
                case ';':
                case '<':
                case '=':
                case '>':
                case '?':
                case '@':
                case '[':
                case '\\':
                case ']':
                case '^':
                case '`':
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                case 'A':
                case 'B':
                case 'C':
                case 'D':
                case 'E':
                case 'F':
                case 'G':
                case 'H':
                case 'I':
                case 'J':
                case 'K':
                case 'L':
                case 'M':
                case 'N':
                case 'O':
                case 'P':
                case 'Q':
                case 'R':
                case 'S':
                case 'T':
                case 'U':
                case 'V':
                case 'W':
                case 'X':
                case 'Y':
                case 'Z':
                    matchRange('A', 'Z');
                    break;
                case '_':
                    match('_');
                    break;
                case 'a':
                case 'b':
                case 'c':
                case 'd':
                case 'e':
                case 'f':
                case 'g':
                case 'h':
                case 'i':
                case 'j':
                case 'k':
                case 'l':
                case 'm':
                case 'n':
                case 'o':
                case 'p':
                case 'q':
                case 'r':
                case 's':
                case 't':
                case 'u':
                case 'v':
                case 'w':
                case 'x':
                case 'y':
                case 'z':
                    matchRange('a', 'z');
                    break;
            }
        }
        if (z && 0 == 0 && 17 != -1) {
            token = makeToken(17);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mVAR_ASSIGN(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        match('=');
        if (LA(1) != '=' && this.transInfo != null && this.transInfo.refRuleRoot != null) {
            this.transInfo.assignToRoot = true;
        }
        if (z && 0 == 0 && 18 != -1) {
            token = makeToken(18);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:27:0x0588  */
    /* JADX WARN: Removed duplicated region for block: B:28:0x05a5  */
    /* JADX WARN: Removed duplicated region for block: B:39:0x088c  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x08a9  */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    protected final void mAST_CONSTRUCTOR(boolean r9) throws antlr.RecognitionException, antlr.CharStreamException, antlr.TokenStreamException {
        /*
            Method dump skipped, instructions count: 2459
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: antlr.actions.csharp.ActionLexer.mAST_CONSTRUCTOR(boolean):void");
    }

    protected final void mTEXT_ARG(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        switch (LA(1)) {
            case '\t':
            case '\n':
            case '\r':
            case ' ':
                mWS(false);
                break;
            case 11:
            case '\f':
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case '!':
            case '#':
            case '%':
            case '&':
            case '(':
            case ')':
            case '*':
            case ',':
            case '-':
            case '.':
            case '/':
            case ':':
            case ';':
            case '<':
            case '=':
            case '>':
            case '?':
            case '@':
            case '[':
            case '\\':
            case ']':
            case '^':
            case '`':
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            case '\"':
            case '$':
            case '\'':
            case '+':
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
            case 'A':
            case 'B':
            case 'C':
            case 'D':
            case 'E':
            case 'F':
            case 'G':
            case 'H':
            case 'I':
            case 'J':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'S':
            case 'T':
            case 'U':
            case 'V':
            case 'W':
            case 'X':
            case 'Y':
            case 'Z':
            case '_':
            case 'a':
            case 'b':
            case 'c':
            case 'd':
            case 'e':
            case 'f':
            case 'g':
            case 'h':
            case 'i':
            case 'j':
            case 'k':
            case 'l':
            case 'm':
            case 'n':
            case 'o':
            case 'p':
            case 'q':
            case 'r':
            case 's':
            case 't':
            case 'u':
            case 'v':
            case 'w':
            case 'x':
            case 'y':
            case 'z':
                break;
        }
        int i = 0;
        while (_tokenSet_11.member(LA(1)) && LA(2) >= 3 && LA(2) <= 255) {
            mTEXT_ARG_ELEMENT(false);
            if (_tokenSet_4.member(LA(1)) && _tokenSet_12.member(LA(2))) {
                mWS(false);
            } else if (!_tokenSet_12.member(LA(1))) {
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            }
            i++;
        }
        if (i < 1) {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 13 != -1) {
            token = makeToken(13);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mTREE_ELEMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        String mapTreeId;
        Token token = null;
        int length = this.text.length();
        switch (LA(1)) {
            case '\"':
                mSTRING(false);
                break;
            case '#':
            case '$':
            case '%':
            case '&':
            case '\'':
            case ')':
            case '*':
            case '+':
            case ',':
            case '-':
            case '.':
            case '/':
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
            case ':':
            case ';':
            case '<':
            case '=':
            case '>':
            case '?':
            case '@':
            case '\\':
            case ']':
            case '^':
            case '`':
            default:
                if (LA(1) == '#' && LA(2) == '(') {
                    int length2 = this.text.length();
                    match('#');
                    this.text.setLength(length2);
                    mTREE(false);
                    break;
                } else if (LA(1) == '#' && LA(2) == '[') {
                    int length3 = this.text.length();
                    match('#');
                    this.text.setLength(length3);
                    mAST_CONSTRUCTOR(false);
                    break;
                } else if (LA(1) == '#' && _tokenSet_13.member(LA(2))) {
                    int length4 = this.text.length();
                    match('#');
                    this.text.setLength(length4);
                    boolean mID_ELEMENT = mID_ELEMENT(true);
                    Token token2 = this._returnToken;
                    if (!mID_ELEMENT && (mapTreeId = this.generator.mapTreeId(token2.getText(), null)) != null) {
                        this.text.setLength(length);
                        this.text.append(mapTreeId);
                        break;
                    }
                } else if (LA(1) == '#' && LA(2) == '#') {
                    match("##");
                    if (this.currentRule != null) {
                        String stringBuffer = new StringBuffer().append(this.currentRule.getRuleName()).append("_AST").toString();
                        this.text.setLength(length);
                        this.text.append(stringBuffer);
                        break;
                    } else {
                        reportError("\"##\" not valid in this context");
                        this.text.setLength(length);
                        this.text.append("##");
                        break;
                    }
                } else {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
            case '(':
                mTREE(false);
                break;
            case 'A':
            case 'B':
            case 'C':
            case 'D':
            case 'E':
            case 'F':
            case 'G':
            case 'H':
            case 'I':
            case 'J':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'S':
            case 'T':
            case 'U':
            case 'V':
            case 'W':
            case 'X':
            case 'Y':
            case 'Z':
            case '_':
            case 'a':
            case 'b':
            case 'c':
            case 'd':
            case 'e':
            case 'f':
            case 'g':
            case 'h':
            case 'i':
            case 'j':
            case 'k':
            case 'l':
            case 'm':
            case 'n':
            case 'o':
            case 'p':
            case 'q':
            case 'r':
            case 's':
            case 't':
            case 'u':
            case 'v':
            case 'w':
            case 'x':
            case 'y':
            case 'z':
                mID_ELEMENT(false);
                break;
            case '[':
                mAST_CONSTRUCTOR(false);
                break;
        }
        if (z && 0 == 0 && 9 != -1) {
            token = makeToken(9);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    /* JADX WARN: Can't fix incorrect switch cases order, some code will duplicate */
    /* JADX WARN: Removed duplicated region for block: B:37:0x0590  */
    /* JADX WARN: Removed duplicated region for block: B:38:0x05ad  */
    /* JADX WARN: Removed duplicated region for block: B:40:0x05c6 A[FALL_THROUGH] */
    /*
        Code decompiled incorrectly, please refer to instructions dump.
        To view partially-correct code enable 'Show inconsistent code' option in preferences
    */
    protected final boolean mID_ELEMENT(boolean r9) throws antlr.RecognitionException, antlr.CharStreamException, antlr.TokenStreamException {
        /*
            Method dump skipped, instructions count: 2591
            To view this dump change 'Code comments level' option to 'DEBUG'
        */
        throw new UnsupportedOperationException("Method not decompiled: antlr.actions.csharp.ActionLexer.mID_ELEMENT(boolean):boolean");
    }

    protected final void mAST_CTOR_ELEMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        if (LA(1) == '\"' && LA(2) >= 3 && LA(2) <= 255 && LA(3) >= 3 && LA(3) <= 255) {
            mSTRING(false);
        } else if (_tokenSet_19.member(LA(1)) && LA(2) >= 3 && LA(2) <= 255) {
            mTREE_ELEMENT(false);
        } else if (LA(1) >= '0' && LA(1) <= '9') {
            mINT(false);
        } else {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 11 != -1) {
            token = makeToken(11);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mINT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        int i = 0;
        while (LA(1) >= '0' && LA(1) <= '9') {
            mDIGIT(false);
            i++;
        }
        if (i < 1) {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 26 != -1) {
            token = makeToken(26);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mARG(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        switch (LA(1)) {
            case '\'':
                mCHAR(false);
                break;
            case '(':
            case ')':
            case '*':
            case '+':
            case ',':
            case '-':
            case '.':
            case '/':
            default:
                if (_tokenSet_19.member(LA(1)) && LA(2) >= 3 && LA(2) <= 255 && LA(3) >= 3 && LA(3) <= 255) {
                    mTREE_ELEMENT(false);
                    break;
                } else if (LA(1) == '\"' && LA(2) >= 3 && LA(2) <= 255 && LA(3) >= 3 && LA(3) <= 255) {
                    mSTRING(false);
                    break;
                } else {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
                mINT_OR_FLOAT(false);
                break;
        }
        while (_tokenSet_20.member(LA(1)) && _tokenSet_21.member(LA(2)) && LA(3) >= 3 && LA(3) <= 255) {
            switch (LA(1)) {
                case '\t':
                case '\n':
                case '\r':
                case ' ':
                    mWS(false);
                    break;
                case '*':
                case '+':
                case '-':
                case '/':
                    break;
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            }
            switch (LA(1)) {
                case '*':
                    match('*');
                    break;
                case '+':
                    match('+');
                    break;
                case ',':
                case '.':
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                case '-':
                    match('-');
                    break;
                case '/':
                    match('/');
                    break;
            }
            switch (LA(1)) {
                case '\t':
                case '\n':
                case '\r':
                case ' ':
                    mWS(false);
                    break;
                case 11:
                case '\f':
                case 14:
                case 15:
                case 16:
                case 17:
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case '!':
                case '$':
                case '%':
                case '&':
                case ')':
                case '*':
                case '+':
                case ',':
                case '-':
                case '.':
                case '/':
                case ':':
                case ';':
                case '<':
                case '=':
                case '>':
                case '?':
                case '@':
                case '\\':
                case ']':
                case '^':
                case '`':
                default:
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                case '\"':
                case '#':
                case '\'':
                case '(':
                case '0':
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                case '7':
                case '8':
                case '9':
                case 'A':
                case 'B':
                case 'C':
                case 'D':
                case 'E':
                case 'F':
                case 'G':
                case 'H':
                case 'I':
                case 'J':
                case 'K':
                case 'L':
                case 'M':
                case 'N':
                case 'O':
                case 'P':
                case 'Q':
                case 'R':
                case 'S':
                case 'T':
                case 'U':
                case 'V':
                case 'W':
                case 'X':
                case 'Y':
                case 'Z':
                case '[':
                case '_':
                case 'a':
                case 'b':
                case 'c':
                case 'd':
                case 'e':
                case 'f':
                case 'g':
                case 'h':
                case 'i':
                case 'j':
                case 'k':
                case 'l':
                case 'm':
                case 'n':
                case 'o':
                case 'p':
                case 'q':
                case 'r':
                case 's':
                case 't':
                case 'u':
                case 'v':
                case 'w':
                case 'x':
                case 'y':
                case 'z':
                    break;
            }
            mARG(false);
        }
        if (z && 0 == 0 && 16 != -1) {
            token = makeToken(16);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mTEXT_ARG_ELEMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        switch (LA(1)) {
            case '\"':
                mSTRING(false);
                break;
            case '#':
            case '%':
            case '&':
            case '(':
            case ')':
            case '*':
            case ',':
            case '-':
            case '.':
            case '/':
            case ':':
            case ';':
            case '<':
            case '=':
            case '>':
            case '?':
            case '@':
            case '[':
            case '\\':
            case ']':
            case '^':
            case '`':
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            case '$':
                mTEXT_ITEM(false);
                break;
            case '\'':
                mCHAR(false);
                break;
            case '+':
                match('+');
                break;
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
                mINT_OR_FLOAT(false);
                break;
            case 'A':
            case 'B':
            case 'C':
            case 'D':
            case 'E':
            case 'F':
            case 'G':
            case 'H':
            case 'I':
            case 'J':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'S':
            case 'T':
            case 'U':
            case 'V':
            case 'W':
            case 'X':
            case 'Y':
            case 'Z':
            case '_':
            case 'a':
            case 'b':
            case 'c':
            case 'd':
            case 'e':
            case 'f':
            case 'g':
            case 'h':
            case 'i':
            case 'j':
            case 'k':
            case 'l':
            case 'm':
            case 'n':
            case 'o':
            case 'p':
            case 'q':
            case 'r':
            case 's':
            case 't':
            case 'u':
            case 'v':
            case 'w':
            case 'x':
            case 'y':
            case 'z':
                mTEXT_ARG_ID_ELEMENT(false);
                break;
        }
        if (z && 0 == 0 && 14 != -1) {
            token = makeToken(14);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mTEXT_ARG_ID_ELEMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        mID(true);
        Token token2 = this._returnToken;
        if (_tokenSet_4.member(LA(1)) && _tokenSet_22.member(LA(2))) {
            int length2 = this.text.length();
            mWS(false);
            this.text.setLength(length2);
        } else if (!_tokenSet_22.member(LA(1))) {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        switch (LA(1)) {
            case '\t':
            case '\n':
            case '\r':
            case ' ':
            case '\"':
            case '$':
            case '\'':
            case ')':
            case '+':
            case ',':
            case '0':
            case '1':
            case '2':
            case '3':
            case '4':
            case '5':
            case '6':
            case '7':
            case '8':
            case '9':
            case 'A':
            case 'B':
            case 'C':
            case 'D':
            case 'E':
            case 'F':
            case 'G':
            case 'H':
            case 'I':
            case 'J':
            case 'K':
            case 'L':
            case 'M':
            case 'N':
            case 'O':
            case 'P':
            case 'Q':
            case 'R':
            case 'S':
            case 'T':
            case 'U':
            case 'V':
            case 'W':
            case 'X':
            case 'Y':
            case 'Z':
            case ']':
            case '_':
            case 'a':
            case 'b':
            case 'c':
            case 'd':
            case 'e':
            case 'f':
            case 'g':
            case 'h':
            case 'i':
            case 'j':
            case 'k':
            case 'l':
            case 'm':
            case 'n':
            case 'o':
            case 'p':
            case 'q':
            case 'r':
            case 's':
            case 't':
            case 'u':
            case 'v':
            case 'w':
            case 'x':
            case 'y':
            case 'z':
                break;
            case 11:
            case '\f':
            case 14:
            case 15:
            case 16:
            case 17:
            case 18:
            case 19:
            case 20:
            case 21:
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
            case 30:
            case 31:
            case '!':
            case '#':
            case '%':
            case '&':
            case '*':
            case '/':
            case ':':
            case ';':
            case '<':
            case '=':
            case '>':
            case '?':
            case '@':
            case '\\':
            case '^':
            case '`':
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
            case '(':
                match('(');
                if (_tokenSet_4.member(LA(1)) && _tokenSet_23.member(LA(2)) && LA(3) >= 3 && LA(3) <= 255) {
                    int length3 = this.text.length();
                    mWS(false);
                    this.text.setLength(length3);
                } else if (!_tokenSet_23.member(LA(1)) || LA(2) < 3 || LA(2) > 255) {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                while (_tokenSet_24.member(LA(1)) && LA(2) >= 3 && LA(2) <= 255 && LA(3) >= 3 && LA(3) <= 255) {
                    mTEXT_ARG(false);
                    while (LA(1) == ',') {
                        match(',');
                        mTEXT_ARG(false);
                    }
                }
                switch (LA(1)) {
                    case '\t':
                    case '\n':
                    case '\r':
                    case ' ':
                        int length4 = this.text.length();
                        mWS(false);
                        this.text.setLength(length4);
                    case ')':
                        match(')');
                        break;
                    default:
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
            case '-':
                match("->");
                mTEXT_ARG_ID_ELEMENT(false);
                break;
            case '.':
                match('.');
                mTEXT_ARG_ID_ELEMENT(false);
                break;
            case '[':
                int i = 0;
                while (LA(1) == '[') {
                    match('[');
                    if (_tokenSet_4.member(LA(1)) && _tokenSet_24.member(LA(2)) && LA(3) >= 3 && LA(3) <= 255) {
                        int length5 = this.text.length();
                        mWS(false);
                        this.text.setLength(length5);
                    } else if (!_tokenSet_24.member(LA(1)) || LA(2) < 3 || LA(2) > 255 || LA(3) < 3 || LA(3) > 255) {
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                    }
                    mTEXT_ARG(false);
                    switch (LA(1)) {
                        case '\t':
                        case '\n':
                        case '\r':
                        case ' ':
                            int length6 = this.text.length();
                            mWS(false);
                            this.text.setLength(length6);
                            break;
                        case ']':
                            break;
                        default:
                            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                    }
                    match(']');
                    i++;
                }
                if (i < 1) {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
        }
        if (z && 0 == 0 && 15 != -1) {
            token = makeToken(15);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mINT_OR_FLOAT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        int i = 0;
        while (LA(1) >= '0' && LA(1) <= '9' && _tokenSet_25.member(LA(2))) {
            mDIGIT(false);
            i++;
        }
        if (i < 1) {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (LA(1) == 'L' && _tokenSet_26.member(LA(2))) {
            match('L');
        } else if (LA(1) == 'l' && _tokenSet_26.member(LA(2))) {
            match('l');
        } else if (LA(1) == '.') {
            match('.');
            while (LA(1) >= '0' && LA(1) <= '9' && _tokenSet_26.member(LA(2))) {
                mDIGIT(false);
            }
        } else if (!_tokenSet_26.member(LA(1))) {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 27 != -1) {
            token = makeToken(27);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mSL_COMMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        match("//");
        while (LA(1) != '\n' && LA(1) != '\r' && LA(1) >= 3 && LA(1) <= 255 && LA(2) >= 3 && LA(2) <= 255) {
            matchNot((char) 65535);
        }
        if (LA(1) == '\r' && LA(2) == '\n') {
            match("\r\n");
        } else if (LA(1) == '\n') {
            match('\n');
        } else if (LA(1) == '\r') {
            match('\r');
        } else {
            throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        newline();
        if (z && 0 == 0 && 20 != -1) {
            token = makeToken(20);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mML_COMMENT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        match(ScriptUtils.DEFAULT_BLOCK_COMMENT_START_DELIMITER);
        while (true) {
            if (LA(1) == '*' && LA(2) == '/') {
                break;
            }
            if (LA(1) == '\r' && LA(2) == '\n' && LA(3) >= 3 && LA(3) <= 255) {
                match('\r');
                match('\n');
                newline();
            } else if (LA(1) == '\r' && LA(2) >= 3 && LA(2) <= 255 && LA(3) >= 3 && LA(3) <= 255) {
                match('\r');
                newline();
            } else if (LA(1) == '\n' && LA(2) >= 3 && LA(2) <= 255 && LA(3) >= 3 && LA(3) <= 255) {
                match('\n');
                newline();
            } else if (LA(1) < 3 || LA(1) > 255 || LA(2) < 3 || LA(2) > 255 || LA(3) < 3 || LA(3) > 255) {
                break;
            } else {
                matchNot((char) 65535);
            }
        }
        match("*/");
        if (z && 0 == 0 && 21 != -1) {
            token = makeToken(21);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mESC(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        match('\\');
        switch (LA(1)) {
            case '\"':
                match('\"');
                break;
            case '\'':
                match('\'');
                break;
            case '0':
            case '1':
            case '2':
            case '3':
                matchRange('0', '3');
                if (LA(1) >= '0' && LA(1) <= '9' && LA(2) >= 3 && LA(2) <= 255) {
                    mDIGIT(false);
                    if (LA(1) >= '0' && LA(1) <= '9' && LA(2) >= 3 && LA(2) <= 255) {
                        mDIGIT(false);
                        break;
                    } else if (LA(1) < 3 || LA(1) > 255) {
                        throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                    }
                } else if (LA(1) < 3 || LA(1) > 255) {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
            case '4':
            case '5':
            case '6':
            case '7':
                matchRange('4', '7');
                if (LA(1) >= '0' && LA(1) <= '9' && LA(2) >= 3 && LA(2) <= 255) {
                    mDIGIT(false);
                    break;
                } else if (LA(1) < 3 || LA(1) > 255) {
                    throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
                }
                break;
            case '\\':
                match('\\');
                break;
            case 'b':
                match('b');
                break;
            case 'f':
                match('f');
                break;
            case 'n':
                match('n');
                break;
            case 'r':
                match('r');
                break;
            case 't':
                match('t');
                break;
            default:
                throw new NoViableAltForCharException(LA(1), getFilename(), getLine(), getColumn());
        }
        if (z && 0 == 0 && 24 != -1) {
            token = makeToken(24);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    protected final void mDIGIT(boolean z) throws RecognitionException, CharStreamException, TokenStreamException {
        Token token = null;
        int length = this.text.length();
        matchRange('0', '9');
        if (z && 0 == 0 && 25 != -1) {
            token = makeToken(25);
            token.setText(new String(this.text.getBuffer(), length, this.text.length() - length));
        }
        this._returnToken = token;
    }

    private static final long[] mk_tokenSet_0() {
        long[] jArr = new long[8];
        jArr[0] = -103079215112L;
        for (int i = 1; i <= 3; i++) {
            jArr[i] = -1;
        }
        return jArr;
    }

    private static final long[] mk_tokenSet_1() {
        long[] jArr = new long[8];
        jArr[0] = -145135534866440L;
        for (int i = 1; i <= 3; i++) {
            jArr[i] = -1;
        }
        return jArr;
    }

    private static final long[] mk_tokenSet_2() {
        long[] jArr = new long[8];
        jArr[0] = -141407503262728L;
        for (int i = 1; i <= 3; i++) {
            jArr[i] = -1;
        }
        return jArr;
    }

    private static final long[] mk_tokenSet_3() {
        return new long[]{4294977024L, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_4() {
        return new long[]{4294977024L, 0, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_5() {
        return new long[]{1103806604800L, 0, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_6() {
        return new long[]{287959436729787904L, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_7() {
        long[] jArr = new long[8];
        jArr[0] = -17179869192L;
        jArr[1] = -268435457;
        for (int i = 2; i <= 3; i++) {
            jArr[i] = -1;
        }
        return jArr;
    }

    private static final long[] mk_tokenSet_8() {
        long[] jArr = new long[8];
        jArr[0] = -549755813896L;
        jArr[1] = -268435457;
        for (int i = 2; i <= 3; i++) {
            jArr[i] = -1;
        }
        return jArr;
    }

    private static final long[] mk_tokenSet_9() {
        return new long[]{287948901175001088L, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_10() {
        return new long[]{287950056521213440L, 576460746129407998L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_11() {
        return new long[]{287958332923183104L, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_12() {
        return new long[]{287978128427460096L, 576460746532061182L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_13() {
        return new long[]{0, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_14() {
        return new long[]{2306123388973753856L, 671088640, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_15() {
        return new long[]{287952805300282880L, 576460746129407998L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_16() {
        return new long[]{2306051920717948416L, 536870912, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_17() {
        return new long[]{2305843013508670976L, 0, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_18() {
        return new long[]{208911504254464L, 536870912, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_19() {
        return new long[]{1151051235328L, 576460746129407998L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_20() {
        return new long[]{189120294954496L, 0, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_21() {
        return new long[]{288139722277004800L, 576460746129407998L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_22() {
        return new long[]{288084781055354368L, 576460746666278910L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_23() {
        return new long[]{287960536241415680L, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_24() {
        return new long[]{287958337218160128L, 576460745995190270L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_25() {
        return new long[]{288228817078593024L, 576460746532061182L, 0, 0, 0};
    }

    private static final long[] mk_tokenSet_26() {
        return new long[]{288158448334415360L, 576460746532061182L, 0, 0, 0};
    }
}
