package com.mchange.v2.encounter;

import java.util.WeakHashMap;

/* loaded from: mchange-commons-java-0.2.11.jar:com/mchange/v2/encounter/EqualityEncounterCounter.class */
public class EqualityEncounterCounter extends AbstractEncounterCounter {
    @Override // com.mchange.v2.encounter.AbstractEncounterCounter, com.mchange.v2.encounter.EncounterCounter
    public /* bridge */ /* synthetic */ void resetAll() {
        super.resetAll();
    }

    @Override // com.mchange.v2.encounter.AbstractEncounterCounter, com.mchange.v2.encounter.EncounterCounter
    public /* bridge */ /* synthetic */ long reset(Object obj) {
        return super.reset(obj);
    }

    @Override // com.mchange.v2.encounter.AbstractEncounterCounter, com.mchange.v2.encounter.EncounterCounter
    public /* bridge */ /* synthetic */ long encounter(Object obj) {
        return super.encounter(obj);
    }

    public EqualityEncounterCounter() {
        super(new WeakHashMap());
    }
}
