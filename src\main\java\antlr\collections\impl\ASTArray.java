package antlr.collections.impl;

import antlr.collections.AST;

/* loaded from: antlr-2.7.7.jar:antlr/collections/impl/ASTArray.class */
public class ASTArray {
    public int size = 0;
    public AST[] array;

    public ASTArray(int i) {
        this.array = new AST[i];
    }

    public ASTArray add(AST ast) {
        AST[] astArr = this.array;
        int i = this.size;
        this.size = i + 1;
        astArr[i] = ast;
        return this;
    }
}
