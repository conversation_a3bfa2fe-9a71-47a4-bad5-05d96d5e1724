package antlr.build;

import antlr.Utils;
import java.io.File;
import java.io.IOException;

/* loaded from: antlr-2.7.7.jar:antlr/build/Tool.class */
public class Tool {
    public String os;
    static Class class$antlr$build$Tool;

    public Tool() {
        this.os = null;
        this.os = System.getProperty("os.name");
    }

    public static void main(String[] strArr) {
        if (strArr.length != 1) {
            System.err.println("usage: java antlr.build.Tool action");
        } else {
            new Tool().perform("antlr.build.ANTLR", strArr[0]);
        }
    }

    public void perform(String str, String str2) {
        Class<?> cls;
        if (str == null || str2 == null) {
            error("missing app or action");
            return;
        }
        Class cls2 = null;
        Object obj = null;
        try {
            obj = Utils.createInstanceOf(str);
        } catch (Exception e) {
            try {
                if (!str.startsWith("antlr.build.")) {
                    cls2 = Utils.loadClass(new StringBuffer().append("antlr.build.").append(str).toString());
                }
                error(new StringBuffer().append("no such application ").append(str).toString(), e);
            } catch (Exception e2) {
                error(new StringBuffer().append("no such application ").append(str).toString(), e2);
            }
        }
        if (cls2 == null || obj == null) {
            return;
        }
        try {
            Class cls3 = cls2;
            Class<?>[] clsArr = new Class[1];
            if (class$antlr$build$Tool == null) {
                cls = class$("antlr.build.Tool");
                class$antlr$build$Tool = cls;
            } else {
                cls = class$antlr$build$Tool;
            }
            clsArr[0] = cls;
            cls3.getMethod(str2, clsArr).invoke(obj, this);
        } catch (Exception e3) {
            error(new StringBuffer().append("no such action for application ").append(str).toString(), e3);
        }
    }

    static Class class$(String str) {
        try {
            return Class.forName(str);
        } catch (ClassNotFoundException e) {
            throw new NoClassDefFoundError(e.getMessage());
        }
    }

    public void system(String str) {
        Process exec;
        Runtime runtime = Runtime.getRuntime();
        try {
            log(str);
            if (!this.os.startsWith("Windows")) {
                exec = runtime.exec(new String[]{"sh", "-c", str});
            } else {
                exec = runtime.exec(str);
            }
            StreamScarfer streamScarfer = new StreamScarfer(exec.getErrorStream(), "stderr", this);
            StreamScarfer streamScarfer2 = new StreamScarfer(exec.getInputStream(), "stdout", this);
            streamScarfer.start();
            streamScarfer2.start();
            exec.waitFor();
        } catch (Exception e) {
            error(new StringBuffer().append("cannot exec ").append(str).toString(), e);
        }
    }

    public void antlr(String str) {
        String str2 = null;
        try {
            str2 = new File(str).getParent();
            if (str2 != null) {
                str2 = new File(str2).getCanonicalPath();
            }
        } catch (IOException e) {
            error(new StringBuffer().append("Invalid grammar file: ").append(str).toString());
        }
        if (str2 != null) {
            log(new StringBuffer().append("java antlr.Tool -o ").append(str2).append(" ").append(str).toString());
            new antlr.Tool().doEverything(new String[]{"-o", str2, str});
        }
    }

    public void stdout(String str) {
        System.out.println(str);
    }

    public void stderr(String str) {
        System.err.println(str);
    }

    public void error(String str) {
        System.err.println(new StringBuffer().append("antlr.build.Tool: ").append(str).toString());
    }

    public void log(String str) {
        System.out.println(new StringBuffer().append("executing: ").append(str).toString());
    }

    public void error(String str, Exception exc) {
        System.err.println(new StringBuffer().append("antlr.build.Tool: ").append(str).toString());
        exc.printStackTrace(System.err);
    }
}
