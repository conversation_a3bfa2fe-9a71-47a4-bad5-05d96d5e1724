package antlr.collections.impl;

import java.util.Enumeration;

/* loaded from: antlr-2.7.7.jar:antlr/collections/impl/Vector.class */
public class Vector implements Cloneable {
    protected Object[] data;
    protected int lastElement;

    public Vector() {
        this(10);
    }

    public Vector(int i) {
        this.lastElement = -1;
        this.data = new Object[i];
    }

    public synchronized void appendElement(Object obj) {
        ensureCapacity(this.lastElement + 2);
        Object[] objArr = this.data;
        int i = this.lastElement + 1;
        this.lastElement = i;
        objArr[i] = obj;
    }

    public int capacity() {
        return this.data.length;
    }

    public Object clone() {
        try {
            Vector vector = (Vector) super.clone();
            vector.data = new Object[size()];
            System.arraycopy(this.data, 0, vector.data, 0, size());
            return vector;
        } catch (CloneNotSupportedException e) {
            System.err.println("cannot clone Vector.super");
            return null;
        }
    }

    public synchronized Object elementAt(int i) {
        if (i >= this.data.length) {
            throw new ArrayIndexOutOfBoundsException(new StringBuffer().append(i).append(" >= ").append(this.data.length).toString());
        }
        if (i < 0) {
            throw new ArrayIndexOutOfBoundsException(new StringBuffer().append(i).append(" < 0 ").toString());
        }
        return this.data[i];
    }

    public synchronized Enumeration elements() {
        return new VectorEnumerator(this);
    }

    public synchronized void ensureCapacity(int i) {
        if (i + 1 > this.data.length) {
            Object[] objArr = this.data;
            int length = this.data.length * 2;
            if (i + 1 > length) {
                length = i + 1;
            }
            this.data = new Object[length];
            System.arraycopy(objArr, 0, this.data, 0, objArr.length);
        }
    }

    public synchronized boolean removeElement(Object obj) {
        int i = 0;
        while (i <= this.lastElement && this.data[i] != obj) {
            i++;
        }
        if (i <= this.lastElement) {
            this.data[i] = null;
            int i2 = this.lastElement - i;
            if (i2 > 0) {
                System.arraycopy(this.data, i + 1, this.data, i, i2);
            }
            this.lastElement--;
            return true;
        }
        return false;
    }

    public synchronized void setElementAt(Object obj, int i) {
        if (i >= this.data.length) {
            throw new ArrayIndexOutOfBoundsException(new StringBuffer().append(i).append(" >= ").append(this.data.length).toString());
        }
        this.data[i] = obj;
        if (i > this.lastElement) {
            this.lastElement = i;
        }
    }

    public int size() {
        return this.lastElement + 1;
    }
}
