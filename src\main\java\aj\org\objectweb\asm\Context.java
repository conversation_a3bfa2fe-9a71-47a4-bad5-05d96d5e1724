package aj.org.objectweb.asm;

/* loaded from: aspectjweaver-1.8.0.jar:aj/org/objectweb/asm/Context.class */
class Context {
    Attribute[] a;
    int b;
    char[] c;
    int[] d;
    int e;
    String f;
    String g;
    Label[] h;
    int i;
    TypePath j;
    int o;
    Label[] l;
    Label[] m;
    int[] n;
    int p;
    int q;
    int r;
    Object[] s;
    int t;
    Object[] u;

    Context() {
    }
}
