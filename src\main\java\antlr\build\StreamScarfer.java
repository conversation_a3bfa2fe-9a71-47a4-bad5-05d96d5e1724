package antlr.build;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

/* loaded from: antlr-2.7.7.jar:antlr/build/StreamScarfer.class */
class StreamScarfer extends Thread {
    InputStream is;
    String type;
    Tool tool;

    StreamScarfer(InputStream inputStream, String str, Tool tool) {
        this.is = inputStream;
        this.type = str;
        this.tool = tool;
    }

    @Override // java.lang.Thread, java.lang.Runnable
    public void run() {
        try {
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(this.is));
            while (true) {
                String readLine = bufferedReader.readLine();
                if (readLine != null) {
                    if (this.type == null || this.type.equals("stdout")) {
                        this.tool.stdout(readLine);
                    } else {
                        this.tool.stderr(readLine);
                    }
                } else {
                    return;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
