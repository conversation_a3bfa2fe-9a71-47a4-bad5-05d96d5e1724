package kr.co.hanshinit.NeoCMS.utl.sim.service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.sql.SQLException;
import kr.co.hanshinit.NeoCMS.cmm.service.CubeOne;
import kr.co.hanshinit.NeoCMS.cmm.util.DBCryptUtil;
import kr.co.hanshinit.NeoCMS.cmm.util.LoggingUtil;
import org.apache.commons.net.util.Base64;

/* JADX WARN: Classes with same name are omitted:
  
 */
/* loaded from: FileScrty.class */
public class FileScrty {
    static final int BUFFER_SIZE = 1024;

    public String encryptPasswordCubeOne(String password, String id) throws NoSuchAlgorithmException {
        String encpw = password;
        if (password != null && id != null) {
            CubeOne co = new CubeOne(password, CubeOne.SHA, "tnUserInfo", "password");
            try {
                encpw = DBCryptUtil.Encrypt(co);
            } catch (SQLException e) {
                LoggingUtil.log(e);
            }
            if (password.equals(encpw)) {
                encpw = encryptPassword(password, id);
            }
        }
        return encpw;
    }

    public static String encryptPassword(String password, String id) throws NoSuchAlgorithmException {
        if (password == null || "".equals(password) || id == null) {
            return "";
        }
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.reset();
        md.update(id.getBytes());
        byte[] hashValue = md.digest(password.getBytes());
        return new String(Base64.encodeBase64(hashValue));
    }

    protected static String encodeBinary(byte[] data) throws Exception {
        if (data == null) {
            return "";
        }
        return new String(Base64.encodeBase64(data));
    }

    protected static byte[] decodeBinary(String data) throws Exception {
        return Base64.decodeBase64(data.getBytes());
    }

    protected static String encryptPassword(String data, byte[] salt) throws NoSuchAlgorithmException {
        if (data == null) {
            return "";
        }
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.reset();
        md.update(salt);
        byte[] hashValue = md.digest(data.getBytes());
        return new String(Base64.encodeBase64(hashValue));
    }

    protected static boolean checkPassword(String data, String encoded, byte[] salt) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        md.reset();
        md.update(salt);
        byte[] hashValue = md.digest(data.getBytes());
        return MessageDigest.isEqual(hashValue, Base64.decodeBase64(encoded.getBytes()));
    }
}
