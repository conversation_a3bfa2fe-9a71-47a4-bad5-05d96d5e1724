package antlr.collections.impl;

import antlr.CharFormatter;
import org.apache.lucene.analysis.kr.morph.NounProperty;
import org.exolab.castor.persist.spi.QueryExpression;

/* loaded from: antlr-2.7.7.jar:antlr/collections/impl/BitSet.class */
public class BitSet implements Cloneable {
    protected static final int BITS = 64;
    protected static final int NIBBLE = 4;
    protected static final int LOG_BITS = 6;
    protected static final int MOD_MASK = 63;
    protected long[] bits;

    public BitSet() {
        this(64);
    }

    public BitSet(long[] jArr) {
        this.bits = jArr;
    }

    public BitSet(int i) {
        this.bits = new long[((i - 1) >> 6) + 1];
    }

    public void add(int i) {
        int wordNumber = wordNumber(i);
        if (wordNumber >= this.bits.length) {
            growToInclude(i);
        }
        long[] jArr = this.bits;
        jArr[wordNumber] = jArr[wordNumber] | bitMask(i);
    }

    public BitSet and(BitSet bitSet) {
        BitSet bitSet2 = (BitSet) clone();
        bitSet2.andInPlace(bitSet);
        return bitSet2;
    }

    public void andInPlace(BitSet bitSet) {
        int min = Math.min(this.bits.length, bitSet.bits.length);
        for (int i = min - 1; i >= 0; i--) {
            long[] jArr = this.bits;
            int i2 = i;
            jArr[i2] = jArr[i2] & bitSet.bits[i];
        }
        for (int i3 = min; i3 < this.bits.length; i3++) {
            this.bits[i3] = 0;
        }
    }

    private static final long bitMask(int i) {
        return 1 << (i & 63);
    }

    public void clear() {
        for (int length = this.bits.length - 1; length >= 0; length--) {
            this.bits[length] = 0;
        }
    }

    public void clear(int i) {
        int wordNumber = wordNumber(i);
        if (wordNumber >= this.bits.length) {
            growToInclude(i);
        }
        long[] jArr = this.bits;
        jArr[wordNumber] = jArr[wordNumber] & (bitMask(i) ^ (-1));
    }

    public Object clone() {
        try {
            BitSet bitSet = (BitSet) super.clone();
            bitSet.bits = new long[this.bits.length];
            System.arraycopy(this.bits, 0, bitSet.bits, 0, this.bits.length);
            return bitSet;
        } catch (CloneNotSupportedException e) {
            throw new InternalError();
        }
    }

    public int degree() {
        int i = 0;
        for (int length = this.bits.length - 1; length >= 0; length--) {
            long j = this.bits[length];
            if (j != 0) {
                for (int i2 = 63; i2 >= 0; i2--) {
                    if ((j & (1 << i2)) != 0) {
                        i++;
                    }
                }
            }
        }
        return i;
    }

    public boolean equals(Object obj) {
        if (obj != null && (obj instanceof BitSet)) {
            BitSet bitSet = (BitSet) obj;
            int min = Math.min(this.bits.length, bitSet.bits.length);
            int i = min;
            do {
                int i2 = i;
                i = i2 - 1;
                if (i2 <= 0) {
                    if (this.bits.length > min) {
                        int length = this.bits.length;
                        do {
                            int i3 = length;
                            length = i3 - 1;
                            if (i3 <= min) {
                                return true;
                            }
                        } while (this.bits[length] == 0);
                        return false;
                    }
                    if (bitSet.bits.length > min) {
                        int length2 = bitSet.bits.length;
                        do {
                            int i4 = length2;
                            length2 = i4 - 1;
                            if (i4 <= min) {
                                return true;
                            }
                        } while (bitSet.bits[length2] == 0);
                        return false;
                    }
                    return true;
                }
            } while (this.bits[i] == bitSet.bits[i]);
            return false;
        }
        return false;
    }

    public static Vector getRanges(int[] iArr) {
        if (iArr.length == 0) {
            return null;
        }
        int i = iArr[0];
        int i2 = iArr[iArr.length - 1];
        if (iArr.length <= 2) {
            return null;
        }
        Vector vector = new Vector(5);
        for (int i3 = 0; i3 < iArr.length - 2; i3++) {
            int length = iArr.length - 1;
            int i4 = i3 + 1;
            while (true) {
                if (i4 >= iArr.length) {
                    break;
                }
                if (iArr[i4] == iArr[i4 - 1] + 1) {
                    i4++;
                } else {
                    length = i4 - 1;
                    break;
                }
            }
            if (length - i3 > 2) {
                vector.appendElement(new IntRange(iArr[i3], iArr[length]));
            }
        }
        return vector;
    }

    public void growToInclude(int i) {
        long[] jArr = new long[Math.max(this.bits.length << 1, numWordsToHold(i))];
        System.arraycopy(this.bits, 0, jArr, 0, this.bits.length);
        this.bits = jArr;
    }

    public boolean member(int i) {
        int wordNumber = wordNumber(i);
        return wordNumber < this.bits.length && (this.bits[wordNumber] & bitMask(i)) != 0;
    }

    public boolean nil() {
        for (int length = this.bits.length - 1; length >= 0; length--) {
            if (this.bits[length] != 0) {
                return false;
            }
        }
        return true;
    }

    public BitSet not() {
        BitSet bitSet = (BitSet) clone();
        bitSet.notInPlace();
        return bitSet;
    }

    public void notInPlace() {
        for (int length = this.bits.length - 1; length >= 0; length--) {
            this.bits[length] = this.bits[length] ^ (-1);
        }
    }

    public void notInPlace(int i) {
        notInPlace(0, i);
    }

    public void notInPlace(int i, int i2) {
        growToInclude(i2);
        for (int i3 = i; i3 <= i2; i3++) {
            int wordNumber = wordNumber(i3);
            long[] jArr = this.bits;
            jArr[wordNumber] = jArr[wordNumber] ^ bitMask(i3);
        }
    }

    private final int numWordsToHold(int i) {
        return (i >> 6) + 1;
    }

    public static BitSet of(int i) {
        BitSet bitSet = new BitSet(i + 1);
        bitSet.add(i);
        return bitSet;
    }

    public BitSet or(BitSet bitSet) {
        BitSet bitSet2 = (BitSet) clone();
        bitSet2.orInPlace(bitSet);
        return bitSet2;
    }

    public void orInPlace(BitSet bitSet) {
        if (bitSet.bits.length > this.bits.length) {
            setSize(bitSet.bits.length);
        }
        for (int min = Math.min(this.bits.length, bitSet.bits.length) - 1; min >= 0; min--) {
            long[] jArr = this.bits;
            int i = min;
            jArr[i] = jArr[i] | bitSet.bits[min];
        }
    }

    public void remove(int i) {
        int wordNumber = wordNumber(i);
        if (wordNumber >= this.bits.length) {
            growToInclude(i);
        }
        long[] jArr = this.bits;
        jArr[wordNumber] = jArr[wordNumber] & (bitMask(i) ^ (-1));
    }

    private void setSize(int i) {
        long[] jArr = new long[i];
        System.arraycopy(this.bits, 0, jArr, 0, Math.min(i, this.bits.length));
        this.bits = jArr;
    }

    public int size() {
        return this.bits.length << 6;
    }

    public int lengthInLongWords() {
        return this.bits.length;
    }

    public boolean subset(BitSet bitSet) {
        if (bitSet == null || !(bitSet instanceof BitSet)) {
            return false;
        }
        return and(bitSet).equals(this);
    }

    public void subtractInPlace(BitSet bitSet) {
        if (bitSet == null) {
            return;
        }
        for (int i = 0; i < this.bits.length && i < bitSet.bits.length; i++) {
            long[] jArr = this.bits;
            int i2 = i;
            jArr[i2] = jArr[i2] & (bitSet.bits[i] ^ (-1));
        }
    }

    public int[] toArray() {
        int[] iArr = new int[degree()];
        int i = 0;
        for (int i2 = 0; i2 < (this.bits.length << 6); i2++) {
            if (member(i2)) {
                int i3 = i;
                i++;
                iArr[i3] = i2;
            }
        }
        return iArr;
    }

    public long[] toPackedArray() {
        return this.bits;
    }

    public String toString() {
        return toString(",");
    }

    public String toString(String str) {
        String str2 = "";
        for (int i = 0; i < (this.bits.length << 6); i++) {
            if (member(i)) {
                if (str2.length() > 0) {
                    str2 = new StringBuffer().append(str2).append(str).toString();
                }
                str2 = new StringBuffer().append(str2).append(i).toString();
            }
        }
        return str2;
    }

    public String toString(String str, CharFormatter charFormatter) {
        String str2 = "";
        for (int i = 0; i < (this.bits.length << 6); i++) {
            if (member(i)) {
                if (str2.length() > 0) {
                    str2 = new StringBuffer().append(str2).append(str).toString();
                }
                str2 = new StringBuffer().append(str2).append(charFormatter.literalChar(i)).toString();
            }
        }
        return str2;
    }

    public String toString(String str, Vector vector) {
        if (vector == null) {
            return toString(str);
        }
        String str2 = "";
        for (int i = 0; i < (this.bits.length << 6); i++) {
            if (member(i)) {
                if (str2.length() > 0) {
                    str2 = new StringBuffer().append(str2).append(str).toString();
                }
                if (i >= vector.size()) {
                    str2 = new StringBuffer().append(str2).append("<bad element ").append(i).append(QueryExpression.OpGreater).toString();
                } else if (vector.elementAt(i) == null) {
                    str2 = new StringBuffer().append(str2).append(QueryExpression.OpLess).append(i).append(QueryExpression.OpGreater).toString();
                } else {
                    str2 = new StringBuffer().append(str2).append((String) vector.elementAt(i)).toString();
                }
            }
        }
        return str2;
    }

    public String toStringOfHalfWords() {
        String str = new String();
        for (int i = 0; i < this.bits.length; i++) {
            if (i != 0) {
                str = new StringBuffer().append(str).append(", ").toString();
            }
            str = new StringBuffer().append(new StringBuffer().append(new StringBuffer().append(str).append(this.bits[i] & 4294967295L).append("UL").toString()).append(", ").toString()).append((this.bits[i] >>> 32) & 4294967295L).append("UL").toString();
        }
        return str;
    }

    public String toStringOfWords() {
        String str = new String();
        for (int i = 0; i < this.bits.length; i++) {
            if (i != 0) {
                str = new StringBuffer().append(str).append(", ").toString();
            }
            str = new StringBuffer().append(str).append(this.bits[i]).append(NounProperty.NP_LOCATION).toString();
        }
        return str;
    }

    public String toStringWithRanges(String str, CharFormatter charFormatter) {
        String str2 = "";
        int[] array = toArray();
        if (array.length == 0) {
            return "";
        }
        int i = 0;
        while (i < array.length) {
            int i2 = 0;
            for (int i3 = i + 1; i3 < array.length && array[i3] == array[i3 - 1] + 1; i3++) {
                i2 = i3;
            }
            if (str2.length() > 0) {
                str2 = new StringBuffer().append(str2).append(str).toString();
            }
            if (i2 - i >= 2) {
                str2 = new StringBuffer().append(new StringBuffer().append(new StringBuffer().append(str2).append(charFormatter.literalChar(array[i])).toString()).append("..").toString()).append(charFormatter.literalChar(array[i2])).toString();
                i = i2;
            } else {
                str2 = new StringBuffer().append(str2).append(charFormatter.literalChar(array[i])).toString();
            }
            i++;
        }
        return str2;
    }

    private static final int wordNumber(int i) {
        return i >> 6;
    }
}
