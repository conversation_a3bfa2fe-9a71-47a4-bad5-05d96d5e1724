package antlr.actions.cpp;

/* loaded from: antlr-2.7.7.jar:antlr/actions/cpp/ActionLexerTokenTypes.class */
public interface ActionLexerTokenTypes {
    public static final int EOF = 1;
    public static final int NULL_TREE_LOOKAHEAD = 3;
    public static final int ACTION = 4;
    public static final int STUFF = 5;
    public static final int AST_ITEM = 6;
    public static final int TEXT_ITEM = 7;
    public static final int TREE = 8;
    public static final int TREE_ELEMENT = 9;
    public static final int AST_CONSTRUCTOR = 10;
    public static final int AST_CTOR_ELEMENT = 11;
    public static final int ID_ELEMENT = 12;
    public static final int TEXT_ARG = 13;
    public static final int TEXT_ARG_ELEMENT = 14;
    public static final int TEXT_ARG_ID_ELEMENT = 15;
    public static final int ARG = 16;
    public static final int ID = 17;
    public static final int VAR_ASSIGN = 18;
    public static final int COMMENT = 19;
    public static final int SL_COMMENT = 20;
    public static final int ML_COMMENT = 21;
    public static final int CHAR = 22;
    public static final int STRING = 23;
    public static final int ESC = 24;
    public static final int DIGIT = 25;
    public static final int INT = 26;
    public static final int INT_OR_FLOAT = 27;
    public static final int WS = 28;
}
