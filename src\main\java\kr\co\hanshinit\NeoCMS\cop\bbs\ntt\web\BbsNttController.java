package kr.co.hanshinit.NeoCMS.cop.bbs.ntt.web;

import egovframework.com.cmm.LoginVO;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import kr.co.hanshinit.NeoCMS.cmm.service.CmmUseService;
import kr.co.hanshinit.NeoCMS.cmm.service.FileMngUtil;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.AccesType;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.BbsMode;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.InsertToken;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.Interceptor;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.Mileage;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.MileageSign;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.MileageType;
import kr.co.hanshinit.NeoCMS.cmm.stereotype.RemoveToken;
import kr.co.hanshinit.NeoCMS.cmm.util.DateUtil;
import kr.co.hanshinit.NeoCMS.cmm.util.RandomUtil;
import kr.co.hanshinit.NeoCMS.cmm.util.SessionUtil;
import kr.co.hanshinit.NeoCMS.cmm.util.StringUtil;
import kr.co.hanshinit.NeoCMS.cop.bbs.bfm.service.BbsFieldService;
import kr.co.hanshinit.NeoCMS.cop.bbs.bim.service.BbsInfo;
import kr.co.hanshinit.NeoCMS.cop.bbs.bim.service.BbsInfoService;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsAtchmnflService;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsAtchmnflVO;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsAuthor;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsAuthorService;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsNtt;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsNttAnswer;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsNttComment;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsNttCommentService;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsNttService;
import kr.co.hanshinit.NeoCMS.cop.bbs.ntt.service.BbsNttVO;
import kr.co.hanshinit.NeoCMS.cop.bbs.service.BbsService;
import kr.co.hanshinit.NeoCMS.cop.bbs.service.BbsVO;
import kr.co.hanshinit.NeoCMS.cop.mileage.service.TnUserPointDtlsService;
import kr.co.hanshinit.NeoCMS.sym.cma.cdc.service.CmmnDetailCode;
import kr.co.hanshinit.NeoCMS.sym.cma.cdc.service.CmmnDetailCodeService;
import kr.co.hanshinit.NeoCMS.sym.dep.emp.service.Employee;
import kr.co.hanshinit.NeoCMS.sym.dep.emp.service.EmployeeService;
import kr.co.hanshinit.NeoCMS.sym.dep.service.Department;
import kr.co.hanshinit.NeoCMS.sym.dep.service.DepartmentService;
import kr.co.hanshinit.NeoCMS.tag.pagination.NeoPaginationInfo;
import kr.co.hanshinit.NeoCMS.uat.uia.service.LoginUtil;
import kr.co.hanshinit.NeoCMS.uss.umt.service.UserInfo;
import kr.co.hanshinit.NeoCMS.uss.umt.service.UserInfoService;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartHttpServletRequest;

/* JADX WARN: Classes with same name are omitted:
  BbsNttController.class_20221220
  BbsNttController.class_20230306
  BbsNttController.class_20230428
  BbsNttController.class_20240624
  BbsNttController.class_20241127
 */
@Controller
/* loaded from: BbsNttController.class */
public class BbsNttController {
    protected Logger log = LoggerFactory.getLogger(getClass());

    @Resource(name = "bbsNttServiceReverse")
    private BbsNttService bbsNttService;

    @Resource(name = "bbsAtchmnflService")
    private BbsAtchmnflService bbsAtchmnflService;

    @Resource(name = "bbsInfoService")
    private BbsInfoService bbsInfoService;

    @Resource(name = "bbsFieldService")
    private BbsFieldService bbsFieldService;

    @Resource(name = "bbsService")
    private BbsService bbsService;

    @Resource(name = "bbsNttCommentService")
    private BbsNttCommentService bbsNttCommentService;

    @Resource(name = "cmmUseService")
    private CmmUseService cmmUseService;

    @Resource(name = "cmmnDetailCodeService")
    private CmmnDetailCodeService cmmnDetailCodeService;

    @Resource(name = "FileMngUtil")
    private FileMngUtil fileMngUtil;

    @Resource(name = "bbsAuthorService")
    private BbsAuthorService bbsAuthorService;

    @Resource(name = "userInfoService")
    private UserInfoService userInfoService;

    @Resource(name = "employeeService")
    private EmployeeService employeeService;

    @Resource(name = "departmentService")
    private DepartmentService departmentService;

    @Resource(name = "tnUserPointDtlsService")
    private TnUserPointDtlsService tnUserPointDtlsService;

    public int selectNttList(BbsAuthor bbsAuthor, BbsInfo bbsInfo, BbsNttVO bbsNttVO, String siteId, int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        if (bbsAuthor == null) {
            bbsAuthor = new BbsAuthor();
        }
        if (!bbsAuthor.isAdminAuth()) {
            bbsNttVO.setIncludeDeletedAt("");
        }
        model.addAttribute("bbsNo", Integer.valueOf(bbsNo));
        if (bbsInfo == null) {
            bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        }
        if ("LSSE04".equals(bbsInfo.getListShowSe()) && !bbsAuthor.isAdminAuth() && !bbsAuthor.isListAuth()) {
            return 0;
        }
        bbsNttVO.setIndvdlzShowAt(this.bbsNttService.isIndvdlzShowAt(bbsInfo, bbsNttVO));
        if ("Y".equals(bbsInfo.getNoticeUseAt())) {
            List<BbsNttVO> bbsNttNoticeList = this.bbsNttService.selectBbsNttNoticeList(bbsInfo, bbsNttVO);
            model.addAttribute("bbsNttNoticeList", bbsNttNoticeList);
        }
        if ("LSSE03".equals(bbsInfo.getListShowSe()) && !bbsAuthor.isAdminAuth() && !bbsAuthor.isListAuth()) {
            if (!LoginUtil.isLogined()) {
                bbsNttVO.setWrterDplctCode("!DPLCT_CD_IS_NOT_SET_DPLCT_CD_IS_NOT_SET_DPLCT_CD_IS_NOT_SET_DPLCT_CD_IS_NOT_SET_DPLCT_CD_IS_NOT_SET_DPLCT_CD_IS_NOT_SET!");
            } else {
                bbsNttVO.setWrterDplctCode(LoginUtil.getPin());
            }
        }
        if (!"0".equals(bbsInfo.getNttShowPd()) && !bbsAuthor.isAdminAuth() && !bbsAuthor.isListAuth()) {
            bbsNttVO.setNttShowPd(bbsInfo.getNttShowPd());
        }
        bbsNttVO.setPageUnit(bbsInfo.getListCo());
        bbsNttVO.setPageSize(bbsInfo.getPageCo());
        bbsNttVO.setReplySe(bbsInfo.getReplySe());
        if (bbsInfo.getBbsNo() == 472 && !bbsAuthor.isAdminAuth()) {
            bbsNttVO.setOthbcAt("Y");
        }
        if (bbsNo == 42) {
            bbsNttVO.setNoticeDe("NOTICEDE");
        }
        int totCnt = this.bbsNttService.selectBbsNttTotCnt(bbsNttVO);
        NeoPaginationInfo neoPaginationInfo = bbsNttVO.getNeoPaginationInfo(totCnt);
        model.addAttribute("paginationInfo", neoPaginationInfo);
        List<BbsNttVO> bbsNttList = this.bbsNttService.selectBbsNttList(bbsInfo, bbsNttVO);
        model.addAttribute("bbsNttList", bbsNttList);
        if ("RTSY03".equals(bbsInfo.getReplySe())) {
            String langCode = (String) request.getAttribute("siteLangCode");
            if (StringUtils.isBlank(langCode)) {
                langCode = "ko_KR";
            }
            List<CmmnDetailCode> answerSttList = null;
            if (model.get("answerSttList") == null) {
                answerSttList = this.cmmnDetailCodeService.selectCmmnDetailCodeLIstByCodeId("BANSST");
                model.addAttribute("answerSttList", answerSttList);
            }
            if (model.get("answerSttMap") == null) {
                if (model.get("answerSttList") == null) {
                    answerSttList = this.cmmnDetailCodeService.selectCmmnDetailCodeLIstByCodeId("BANSST");
                    model.addAttribute("answerSttList", answerSttList);
                }
                model.addAttribute("answerSttMap", this.cmmUseService.selectCmmnDetailCodeListToMap((List) model.get("answerSttList"), langCode));
            }
            model.addAttribute("answerSttusList", answerSttList);
            model.addAttribute("answerSttusMap", this.cmmUseService.selectCmmnDetailCodeListToMap(answerSttList, langCode));
        }
        return totCnt;
    }

    @RequestMapping({"/{siteId}/selectBbsNttList.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.LIST}, type = {AccesType.BBS})
    public String selectBbsNttList(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        BbsAuthor bbsAuthor = (BbsAuthor) request.getAttribute("bbsAuthor");
        int totCnt = selectNttList(bbsAuthor, null, bbsNttVO, siteId, bbsNo, request, model);
        if (totCnt > 0 && bbsAuthor.isAdminAuth()) {
            BbsVO bbsVO = this.bbsService.selectBbs(bbsNo);
            BbsVO abbsVO = new BbsVO();
            abbsVO.setSiteId(siteId);
            int aTotCnt = this.bbsService.selectBbsTotCnt(bbsVO);
            abbsVO.setFirstIndex(0);
            abbsVO.setLastIndex(aTotCnt);
            List<BbsVO> bbsList = this.bbsService.selectBbsList(abbsVO);
            model.addAttribute("bbsMgrAuth", "Y");
            model.addAttribute("transBbsList", bbsList);
        }
        return "bbs/" + bbsNo + "/list.jsp";
    }

    @RequestMapping({"/{siteId}/inputNttPasswordView.do"})
    @Interceptor({"templateBinding"})
    public String inputNttPasswordView(@PathVariable("siteId") String siteId, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNo);
        if (bbsInfo == null) {
            return this.cmmUseService.backMsg(model, "존재하지 않는 게시판 번호입니다.");
        }
        if (!"Y".equals(bbsInfo.getPasswordUseAt())) {
            return this.cmmUseService.backMsg(model, "비밀번호를 사용하지 않는 게시판입니다.");
        }
        return "NeoCMS/cop/bbs/ntt/inputNttPasswordView";
    }

    @RequestMapping({"/{siteId}/addBbsNttView.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.ADD}, type = {AccesType.BBS})
    @InsertToken
    public String addBbsNttView(@PathVariable("siteId") String siteId, @ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        model.addAttribute("bbsNo", Integer.valueOf(bbsNo));
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNo);
        model.addAttribute("bbsInfo", bbsInfo);
        if (!this.bbsService.isRequestPeriod(bbsInfo)) {
            String pdMssage = bbsInfo.getReqstPdMssage();
            if (pdMssage == null) {
                pdMssage = "신청기간이 아닙니다.";
            }
            return this.cmmUseService.backMsg(model, pdMssage);
        }
        LoginVO loginVO = LoginUtil.getLoginVO();
        if (loginVO != null) {
            bbsNttVO.setWrterNm(loginVO.getName());
            bbsNttVO.setDeptCode(loginVO.getOrgnztId());
            bbsNttVO.setDeptNm(loginVO.getOrgnztNm());
            bbsNttVO.setWrterDplctCode(loginVO.getId());
        } else {
            bbsNttVO.setWrterNm("손님");
            bbsNttVO.setWrterDplctCode(BbsAuthorService.GUEST_DPLCT_PREFIX + DateUtil.getNowDateTime() + RandomUtil.RandomNumberStr(5));
        }
        model.addAttribute("bbsNttVO", bbsNttVO);
        model.addAttribute("organizationFirstList", this.departmentService.selectDepartmentList(new Department()));
        return "bbs/" + bbsNo + "/regist.jsp";
    }

    @RequestMapping({"/{siteId}/addBbsNtt.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.ADD}, type = {AccesType.BBS})
    @RemoveToken
    @Mileage(type = MileageType.BBS, sj = "게시판 글쓰기", sign = MileageSign.PLUS, score = 5)
    public String addBbsNtt(MultipartHttpServletRequest multiRequest, @ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, HttpServletRequest request, ModelMap model) throws Exception {
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        model.addAttribute("bbsInfo", bbsInfo);
        if (!this.bbsService.isRequestPeriod(bbsInfo)) {
            String pdMssage = bbsInfo.getReqstPdMssage();
            if (pdMssage == null) {
                pdMssage = "신청기간이 아닙니다.";
            }
            return this.cmmUseService.backMsg(model, pdMssage);
        }
        LoginVO loginVO = LoginUtil.getLoginVO();
        if (loginVO != null) {
            if (StringUtils.isBlank(bbsNttVO.getWrterNm())) {
                bbsNttVO.setWrterNm(loginVO.getName());
            }
            if (StringUtils.isBlank(bbsNttVO.getDeptNm())) {
                bbsNttVO.setDeptNm(loginVO.getOrgnztNm());
            }
            bbsNttVO.setDeptCode(loginVO.getOrgnztId());
            bbsNttVO.setWrterDplctCode(loginVO.getUniqId());
        } else {
            if (StringUtils.isBlank(bbsNttVO.getWrterNm())) {
                bbsNttVO.setWrterNm("손님");
            }
            bbsNttVO.setWrterDplctCode(BbsAuthorService.GUEST_DPLCT_PREFIX + DateUtil.getNowDateTime() + RandomUtil.RandomNumberStr(5));
        }
        model.addAttribute("bbsNo", Integer.valueOf(bbsNttVO.getBbsNo()));
        model.addAttribute("bbsNtt", bbsNttVO);
        String[] arrValidate = this.bbsNttService.validateBbsNtt(request, bbsNttVO);
        if ("1".equals(arrValidate[0])) {
            return this.cmmUseService.backMsg(model, String.valueOf(arrValidate[1]) + ":" + arrValidate[2]);
        }
        if ("Y".equals(bbsInfo.getIntegrUseAt())) {
            String loginId = SessionUtil.getCurrentLoginId();
            if (StringUtils.isNotBlank(loginId)) {
                UserInfo userInfo = this.userInfoService.selectUserInfo(loginId);
                String emplCode = userInfo.getEmplCode();
                if (StringUtils.isNotBlank(emplCode)) {
                    Employee employee = this.employeeService.selectEmployee(emplCode);
                    if (employee != null) {
                        bbsNttVO.setIntegrDeptCode(employee.getDeptCode());
                    } else {
                        bbsNttVO.setIntegrDeptCode(loginId);
                    }
                } else {
                    bbsNttVO.setIntegrDeptCode(loginId);
                }
            }
        }
        this.bbsNttService.insertBbsNtt(multiRequest, request, bbsNttVO);
        SessionUtil.setSessionValue("POINT_ACTION_URL", "/" + siteId + "/selectBbsNttView.do?nttNo=" + bbsNttVO.getNttNo() + "&bbsNo=" + bbsNttVO.getBbsNo() + "&key=" + ((Object) bbsNttVO.getKey()));
        return this.cmmUseService.redirectMsg(model, "등록을 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/selectBbsNttView.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.VIEW}, type = {AccesType.BBS})
    public String selectBbsNttView(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        model.addAttribute("bbsInfo", bbsInfo);
        BbsAuthor bbsAuthor = (BbsAuthor) request.getAttribute("bbsAuthor");
        if (bbsAuthor == null) {
            bbsAuthor = new BbsAuthor();
        }
        if (!bbsAuthor.isAdminAuth() && "LSSE03".equals(bbsInfo.getListShowSe())) {
            if (!LoginUtil.isLogined()) {
                return this.cmmUseService.backMsg(model, "로그인 후 접근 가능 합니다.");
            }
            bbsNttVO.setWrterDplctCode(LoginUtil.getPin());
        }
        if (!bbsAuthor.isAdminAuth() && !"0".equals(bbsInfo.getNttShowPd())) {
            bbsNttVO.setNttShowPd(bbsInfo.getNttShowPd());
        }
        BbsNttVO bbsNtt = this.bbsNttService.selectBbsNtt(bbsNttVO.getNttNo());
        model.addAttribute("bbsNtt", bbsNtt);
        if ("N".equals(bbsNtt.getHtmlUseAt())) {
            String nttCn = bbsNtt.getNttCn();
            bbsNtt.setNttCn(StringUtil.html2text(nttCn));
        }
        model.addAttribute("bbsAtchmnflList", bbsNtt.getAtchmnflList());
        this.bbsNttService.updateBbsNttRdcnt(request, bbsNttVO);
        if ("RTSY03".equals(bbsInfo.getReplySe())) {
            List<BbsNttAnswer> answerList = bbsNtt.getAnswerList();
            model.addAttribute("bbsNttAnswerList", answerList);
            if (answerList != null) {
                String langCode = (String) request.getAttribute("siteLangCode");
                if (StringUtils.isBlank(langCode)) {
                    langCode = "ko_KR";
                }
                List<CmmnDetailCode> answerSttList = this.cmmnDetailCodeService.selectCmmnDetailCodeLIstByCodeId("BANSST");
                model.addAttribute("answerSttusList", answerSttList);
                model.addAttribute("answerSttusMap", this.cmmUseService.selectCmmnDetailCodeListToMap(answerSttList, langCode));
                List<CmmnDetailCode> resultSttList = this.cmmnDetailCodeService.selectCmmnDetailCodeLIstByCodeId("BANSRS");
                model.addAttribute("resultSttusList", resultSttList);
                model.addAttribute("resultSttusMap", this.cmmUseService.selectCmmnDetailCodeListToMap(resultSttList, langCode));
            }
        }
        bbsNttVO.setIndvdlzShowAt(this.bbsNttService.isIndvdlzShowAt(bbsInfo, bbsNttVO));
        int rnum = -1;
        if ("Y".equals(bbsInfo.getPrevNextShowAt())) {
            bbsNttVO.setReplySe(bbsInfo.getReplySe());
            rnum = this.bbsNttService.selectBbsNttRnum(bbsNttVO);
            if (rnum > 0) {
                bbsNttVO.setRnum(rnum);
                BbsNtt bbsNttPrev = this.bbsNttService.selectBbsNttPrev(bbsNttVO);
                model.addAttribute("bbsNttPrev", bbsNttPrev);
                BbsNtt bbsNttNext = this.bbsNttService.selectBbsNttNext(bbsNttVO);
                model.addAttribute("bbsNttNext", bbsNttNext);
            }
        }
        if ("Y".equals(bbsInfo.getViewListShowAt())) {
            if (rnum == -1) {
                rnum = this.bbsNttService.selectBbsNttRnum(bbsNttVO);
            }
            if (rnum > 0) {
                bbsNttVO.setPageIndex((int) Math.ceil((rnum * 1.0d) / bbsInfo.getListCo()));
                selectNttList(bbsAuthor, bbsInfo, bbsNttVO, siteId, bbsNo, request, model);
            }
        }
        if ("Y".equals(bbsInfo.getAnswerUseAt())) {
            BbsNttComment nttComment = new BbsNttComment();
            nttComment.setNttNo(bbsNttVO.getNttNo());
            nttComment.setPageIndex(1);
            nttComment.setPageUnit(bbsInfo.getListCo());
            nttComment.setPageSize(bbsInfo.getPageCo());
            int totCnt = this.bbsNttCommentService.selectBbsNttCommentTotCnt(nttComment);
            NeoPaginationInfo cmmtPaginationInfo = nttComment.getNeoPaginationInfo(totCnt);
            model.addAttribute("cmmPaginationInfo", cmmtPaginationInfo);
            List<BbsNttComment> bbsNttCommentList = this.bbsNttCommentService.selectBbsNttCommentList(nttComment);
            model.addAttribute("bbsNttCommentList", bbsNttCommentList);
        }
        return "bbs/" + bbsNttVO.getBbsNo() + "/view.jsp";
    }

    @RequestMapping({"/{siteId}/updateBbsNttView.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.UPDATE}, type = {AccesType.BBS})
    public String updateBbsNttView(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        model.addAttribute("bbsNo", Integer.valueOf(bbsNo));
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        model.addAttribute("bbsInfo", bbsInfo);
        BbsNttVO bbsNtt = this.bbsNttService.selectBbsNtt(bbsNttVO.getNttNo());
        model.addAttribute("bbsNtt", bbsNtt);
        model.addAttribute("bbsAtchmnflList", bbsNtt.getAtchmnflList());
        model.addAttribute("organizationFirstList", this.departmentService.selectDepartmentList(new Department()));
        return "bbs/" + bbsNo + "/updt.jsp";
    }

    @RequestMapping({"/{siteId}/updateBbsNtt.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.UPDATE}, type = {AccesType.BBS})
    public String updateBbsNtt(MultipartHttpServletRequest multiRequest, @ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, HttpServletRequest request, ModelMap model) throws Exception {
        BbsNtt origNtt = this.bbsNttService.selectBbsNtt(bbsNttVO);
        if (origNtt == null) {
            return this.cmmUseService.backMsg(model, "대상 게시글이 존재하지 않습니다.");
        }
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        model.addAttribute("bbsInfo", bbsInfo);
        model.addAttribute("bbsNtt", bbsNttVO);
        String[] arrValidate = this.bbsNttService.validateBbsNtt(request, bbsNttVO);
        if ("1".equals(arrValidate[0])) {
            return this.cmmUseService.backMsg(model, String.valueOf(arrValidate[1]) + ":" + arrValidate[2]);
        }
        if ("Y".equals(bbsInfo.getIntegrUseAt())) {
            bbsNttVO.setIntegrDeptCode(origNtt.getIntegrDeptCode());
        }
        if (StringUtils.isBlank(bbsNttVO.getWrterNm())) {
            bbsNttVO.setWrterNm(origNtt.getWrterNm());
        }
        if (StringUtils.isBlank(bbsNttVO.getDeptNm())) {
            bbsNttVO.setDeptNm(origNtt.getDeptNm());
        }
        this.bbsNttService.updateBbsNtt(multiRequest, request, bbsNttVO);
        return this.cmmUseService.redirectMsg(model, "수정을 완료하였습니다.", "/" + siteId + "/selectBbsNttView.do?bbsNo=" + bbsNttVO.getBbsNo() + "&nttNo=" + bbsNttVO.getNttNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/updateBbsNttField.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.UPDATE}, type = {AccesType.BBS})
    public String updateBbsNttField(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, @RequestParam(value = "fieldNm", required = true) String fieldNm, @RequestParam(value = "fieldVal", required = false, defaultValue = "") String fieldVal, HttpServletRequest request, ModelMap model) throws Exception {
        BbsAuthor bbsAuthor = (BbsAuthor) request.getAttribute("bbsAuthor");
        if (bbsAuthor == null || !bbsAuthor.isAdminAuth()) {
            return this.cmmUseService.backMsg(model, "수정권한이 없습니다.");
        }
        BbsNttVO origNttVO = this.bbsNttService.selectBbsNtt(bbsNttVO.getNttNo());
        if (origNttVO == null) {
            return this.cmmUseService.backMsg(model, "변경하고자 하는 행을 찾을 수 없습니다.");
        }
        PropertyUtils.setProperty(origNttVO, fieldNm, fieldVal);
        this.bbsNttService.updateBbsNtt(null, request, origNttVO);
        return this.cmmUseService.redirectMsg(model, "수정을 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/deleteBbsNtt.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.DELETE}, type = {AccesType.BBS})
    @Mileage(type = MileageType.BBS, sj = "게시판 삭제", sign = MileageSign.MINUS, score = 5)
    public String deleteBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, HttpServletRequest request, ModelMap model) throws Exception {
        if (this.bbsNttService.selectBbsNttLwprtCnt(bbsNttVO) != 0) {
            return this.cmmUseService.backMsg(model, "답변글이 있어 삭제할 수 없습니다.");
        }
        this.bbsNttService.updateBbsNttDeleteAt(request, bbsNttVO.getNttNo());
        return this.cmmUseService.redirectMsg(model, "삭제를 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/addBbsNttReplyView.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.REPLY}, type = {AccesType.BBS})
    @InsertToken
    public String addBbsNttReplyView(@PathVariable("siteId") String siteId, @ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @RequestParam("bbsNo") int bbsNo, @RequestParam("nttNo") int nttNo, HttpServletRequest request, ModelMap model) throws Exception {
        model.addAttribute("bbsNo", Integer.valueOf(bbsNo));
        model.addAttribute("nttNo", Integer.valueOf(nttNo));
        LoginVO loginVO = LoginUtil.getLoginVO();
        if (loginVO != null) {
            bbsNttVO.setWrterNm(loginVO.getName());
            bbsNttVO.setDeptCode(loginVO.getOrgnztId());
            bbsNttVO.setDeptNm(loginVO.getOrgnztNm());
            bbsNttVO.setWrterDplctCode(loginVO.getId());
        } else {
            bbsNttVO.setWrterNm("손님");
            bbsNttVO.setWrterDplctCode(BbsAuthorService.GUEST_DPLCT_PREFIX + DateUtil.getNowDateTime() + RandomUtil.RandomNumberStr(5));
        }
        model.addAttribute("bbsNttVO", bbsNttVO);
        model.addAttribute("organizationFirstList", this.departmentService.selectDepartmentList(new Department()));
        return "bbs/" + bbsNo + "/reply.jsp";
    }

    @RequestMapping({"/{siteId}/addBbsNttReply.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.REPLY}, type = {AccesType.BBS})
    @RemoveToken
    public String addBbsNttReply(MultipartHttpServletRequest multiRequest, @ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, HttpServletRequest request, ModelMap model) throws Exception {
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        LoginVO loginVO = LoginUtil.getLoginVO();
        if (loginVO != null) {
            if (StringUtils.isBlank(bbsNttVO.getWrterNm())) {
                bbsNttVO.setWrterNm(loginVO.getName());
            }
            if (StringUtils.isBlank(bbsNttVO.getDeptNm())) {
                bbsNttVO.setDeptNm(loginVO.getOrgnztNm());
            }
            bbsNttVO.setDeptCode(loginVO.getOrgnztId());
            bbsNttVO.setWrterDplctCode(loginVO.getUniqId());
        } else {
            if (StringUtils.isBlank(bbsNttVO.getWrterNm())) {
                bbsNttVO.setWrterNm("손님");
            }
            bbsNttVO.setWrterDplctCode(BbsAuthorService.GUEST_DPLCT_PREFIX + DateUtil.getNowDateTime() + RandomUtil.RandomNumberStr(5));
        }
        model.addAttribute("bbsNo", Integer.valueOf(bbsNttVO.getBbsNo()));
        model.addAttribute("bbsNtt", bbsNttVO);
        String[] arrValidate = this.bbsNttService.validateBbsNtt(request, bbsNttVO);
        if ("1".equals(arrValidate[0])) {
            return this.cmmUseService.backMsg(model, String.valueOf(arrValidate[1]) + ":" + arrValidate[2]);
        }
        if ("Y".equals(bbsInfo.getIntegrUseAt())) {
            int upperNttNo = bbsNttVO.getUpperNttNo();
            BbsNtt upperNtt = this.bbsNttService.selectBbsNtt(upperNttNo);
            if (upperNtt != null) {
                bbsNttVO.setIntegrDeptCode(upperNtt.getIntegrDeptCode());
            }
        }
        this.bbsNttService.insertBbsNttReply(multiRequest, request, bbsNttVO);
        return this.cmmUseService.redirectMsg(model, "등록을 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/selectBbsNttMyList.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.MYLIST}, type = {AccesType.BBS})
    public String selectBbsNttMyList(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        model.addAttribute("bbsNo", Integer.valueOf(bbsNo));
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        bbsNttVO.setWrterDplctCode(LoginUtil.getPin());
        bbsNttVO.setPageUnit(bbsInfo.getListCo());
        bbsNttVO.setPageSize(bbsInfo.getPageCo());
        int totCnt = this.bbsNttService.selectBbsNttTotCnt(bbsNttVO);
        NeoPaginationInfo neoPaginationInfo = bbsNttVO.getNeoPaginationInfo(totCnt);
        model.addAttribute("paginationInfo", neoPaginationInfo);
        bbsNttVO.setIncludeDeletedAt("");
        List<BbsNttVO> bbsNttList = this.bbsNttService.selectBbsNttList(bbsInfo, bbsNttVO);
        model.addAttribute("bbsNttList", bbsNttList);
        BbsVO bbsVO = this.bbsService.selectBbs(bbsNo);
        if ("movie".equals(bbsVO.getSkinId())) {
            int nttNo = 0;
            String strNttNo = StringUtil.nvl(request.getParameter("nttNo"));
            if (StringUtil.isEmpty(strNttNo)) {
                if (bbsNttList.size() != 0) {
                    nttNo = bbsNttList.get(0).getNttNo();
                }
            } else {
                nttNo = Integer.parseInt(strNttNo);
            }
            if (nttNo != 0) {
                BbsNtt bbsNtt = this.bbsNttService.selectBbsNtt(nttNo);
                model.addAttribute("bbsNtt", bbsNtt);
                if ("N".equals(bbsNtt.getHtmlUseAt())) {
                    String nttCn = bbsNtt.getNttCn();
                    bbsNtt.setNttCn(StringUtil.html2text(nttCn));
                }
                List<BbsAtchmnflVO> bbsAtchmnflList = this.bbsAtchmnflService.selectBbsAtchmnflList(request, nttNo, true);
                model.addAttribute("bbsAtchmnflList", bbsAtchmnflList);
            }
        }
        return "bbs/" + bbsNo + "/myList.jsp";
    }

    @RequestMapping({"/{siteId}/selectBbsNttMyView.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.MYVIEW}, type = {AccesType.BBS})
    public String selectBbsNttMyView(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam("bbsNo") int bbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        BbsInfo bbsInfo = this.bbsInfoService.selectBbsInfo(bbsNttVO.getBbsNo());
        model.addAttribute("bbsInfo", bbsInfo);
        BbsNttVO bbsNtt = this.bbsNttService.selectBbsNtt(bbsNttVO.getNttNo());
        model.addAttribute("bbsNtt", bbsNtt);
        bbsNttVO.setWrterDplctCode(LoginUtil.getPin());
        if ("N".equals(bbsNtt.getHtmlUseAt())) {
            String nttCn = bbsNtt.getNttCn();
            bbsNtt.setNttCn(StringUtil.html2text(nttCn));
        }
        model.addAttribute("bbsAtchmnflList", bbsNtt.getAtchmnflList());
        this.bbsNttService.updateBbsNttRdcnt(request, bbsNttVO);
        if ("RTSY03".equals(bbsInfo.getReplySe())) {
            List<BbsNttAnswer> answerList = bbsNtt.getAnswerList();
            model.addAttribute("bbsNttAnswerList", answerList);
            if (answerList != null) {
                String langCode = (String) request.getAttribute("siteLangCode");
                if (StringUtils.isBlank(langCode)) {
                    langCode = "ko_KR";
                }
                List<CmmnDetailCode> answerSttList = this.cmmnDetailCodeService.selectCmmnDetailCodeLIstByCodeId("BANSST");
                model.addAttribute("answerSttusList", answerSttList);
                model.addAttribute("answerSttusMap", this.cmmUseService.selectCmmnDetailCodeListToMap(answerSttList, langCode));
                List<CmmnDetailCode> resultSttList = this.cmmnDetailCodeService.selectCmmnDetailCodeLIstByCodeId("BANSRS");
                model.addAttribute("resultSttusList", resultSttList);
                model.addAttribute("resultSttusMap", this.cmmUseService.selectCmmnDetailCodeListToMap(resultSttList, langCode));
            }
        }
        int rnum = this.bbsNttService.selectBbsNttRnum(bbsNttVO);
        bbsNttVO.setRnum(rnum);
        BbsNtt bbsNttPrev = this.bbsNttService.selectBbsNttPrev(bbsNttVO);
        model.addAttribute("bbsNttPrev", bbsNttPrev);
        BbsNtt bbsNttNext = this.bbsNttService.selectBbsNttNext(bbsNttVO);
        model.addAttribute("bbsNttNext", bbsNttNext);
        if ("Y".equals(bbsInfo.getAnswerUseAt())) {
            BbsNttComment nttComment = new BbsNttComment();
            nttComment.setNttNo(bbsNttVO.getNttNo());
            nttComment.setPageIndex(1);
            nttComment.setPageUnit(bbsInfo.getListCo());
            nttComment.setPageSize(bbsInfo.getPageCo());
            int totCnt = this.bbsNttCommentService.selectBbsNttCommentTotCnt(nttComment);
            NeoPaginationInfo cmmtPaginationInfo = nttComment.getNeoPaginationInfo(totCnt);
            model.addAttribute("paginationInfo", cmmtPaginationInfo);
            List<BbsNttComment> bbsNttCommentList = this.bbsNttCommentService.selectBbsNttCommentList(nttComment);
            model.addAttribute("bbsNttCommentList", bbsNttCommentList);
        }
        return "bbs/" + bbsNttVO.getBbsNo() + "/myView.jsp";
    }

    @RequestMapping({"/{siteId}/moveMultiBbsNtt.do"})
    public String moveMultiBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, @RequestParam(value = "chkid", required = false) Integer[] chkid, @RequestParam(value = "tBbsNo", required = false) Integer tBbsNo, HttpServletRequest request, ModelMap model) throws Exception {
        if (!LoginUtil.isAdmin() && !this.bbsAuthorService.authorSiteMngr(siteId) && !this.bbsAuthorService.authorBbsMngr(bbsNttVO.getBbsNo())) {
            return this.cmmUseService.backMsg(model, "게시물 이동권한이 없습니다.");
        }
        BbsAuthor auth = this.bbsAuthorService.authorBbs(request, siteId, tBbsNo.intValue(), 0, 0, 0, "", BbsMode.ADD);
        if (!auth.isAddAuth()) {
            return this.cmmUseService.backMsg(model, "게시물을 이동할 게시판에 쓰기 권한이 없습니다.");
        }
        if (tBbsNo != null && chkid != null && chkid.length > 0) {
            for (int i = chkid.length - 1; i >= 0; i--) {
                this.bbsNttService.moveBbsNtt(chkid[i], tBbsNo);
            }
        }
        return this.cmmUseService.redirectMsg(model, "이동을 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/deleteMultiBbsNtt.do"})
    public String deleteMultiBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, @RequestParam(value = "chkid", required = false) Integer[] chkid, HttpServletRequest request, ModelMap model) throws Exception {
        if (!LoginUtil.isAdmin() && !this.bbsAuthorService.authorSiteMngr(siteId) && !this.bbsAuthorService.authorBbsMngr(bbsNttVO.getBbsNo())) {
            return this.cmmUseService.backMsg(model, "게시물 삭제권한이 없습니다.");
        }
        if (chkid != null && chkid.length > 0) {
            for (int i = chkid.length - 1; i >= 0; i--) {
                this.bbsNttService.updateBbsNttDeleteAt(request, chkid[i].intValue());
            }
        }
        return this.cmmUseService.redirectMsg(model, "삭제를 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/updateMultiBbsNttField.do"})
    public String updateMultiBbsNttField(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, @RequestParam(value = "chkid", required = false) Integer[] chkid, @RequestParam(value = "fieldNm", required = true) String fieldNm, @RequestParam(value = "fieldVal", required = false, defaultValue = "") String fieldVal, HttpServletRequest request, ModelMap model) throws Exception {
        if (!LoginUtil.isAdmin() && !this.bbsAuthorService.authorSiteMngr(siteId) && !this.bbsAuthorService.authorBbsMngr(bbsNttVO.getBbsNo())) {
            return this.cmmUseService.backMsg(model, "게시물 수정 권한이 없습니다.");
        }
        if (chkid != null && chkid.length > 0) {
            for (int i = chkid.length - 1; i >= 0; i--) {
                BbsNttVO origNttVO = this.bbsNttService.selectBbsNtt(chkid[i].intValue());
                if (origNttVO == null) {
                    return this.cmmUseService.backMsg(model, "변경하고자 하는 행을 찾을 수 없습니다.");
                }
                PropertyUtils.setProperty(origNttVO, fieldNm, fieldVal);
                this.bbsNttService.updateBbsNtt(null, request, origNttVO);
            }
        }
        return this.cmmUseService.redirectMsg(model, "수정을 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/restoreBbsNtt.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.RESTORE}, type = {AccesType.BBS})
    public String restoreBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, HttpServletRequest request, ModelMap model) throws Exception {
        this.bbsNttService.restoreBbsNttDeleteAt(request, bbsNttVO.getNttNo());
        return this.cmmUseService.redirectMsg(model, "복구를 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/restoreMultiBbsNtt.do"})
    public String restoreMultiBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, @RequestParam(value = "chkid", required = false) Integer[] chkid, HttpServletRequest request, ModelMap model) throws Exception {
        if (!LoginUtil.isAdmin() && !this.bbsAuthorService.authorSiteMngr(siteId) && !this.bbsAuthorService.authorBbsMngr(bbsNttVO.getBbsNo())) {
            return this.cmmUseService.backMsg(model, "게시물 복구권한이 없습니다.");
        }
        if (chkid != null && chkid.length > 0) {
            for (int i = chkid.length - 1; i >= 0; i--) {
                this.bbsNttService.restoreBbsNttDeleteAt(request, chkid[i].intValue());
            }
        }
        return this.cmmUseService.redirectMsg(model, "복구를 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/removeBbsNtt.do"})
    @Interceptor(value = {"bbsAuthorBinding", "templateBinding"}, mode = {BbsMode.REMOVE}, type = {AccesType.BBS})
    public String removeBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, HttpServletRequest request, ModelMap model) throws Exception {
        if (this.bbsNttService.selectBbsNttLwprtCnt(bbsNttVO) != 0) {
            return this.cmmUseService.backMsg(model, "답변글이 있어 삭제할 수 없습니다.");
        }
        this.bbsNttService.deleteBbsNtt(request, bbsNttVO.getNttNo());
        return this.cmmUseService.redirectMsg(model, "DB삭제를 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }

    @RequestMapping({"/{siteId}/removeMultiBbsNtt.do"})
    public String removeMultiBbsNtt(@ModelAttribute("bbsNttVO") BbsNttVO bbsNttVO, @PathVariable("siteId") String siteId, @RequestParam(value = "key", required = false) Integer key, @RequestParam(value = "chkid", required = false) Integer[] chkid, HttpServletRequest request, ModelMap model) throws Exception {
        if (!LoginUtil.isAdmin() && !this.bbsAuthorService.authorSiteMngr(siteId) && !this.bbsAuthorService.authorBbsMngr(bbsNttVO.getBbsNo())) {
            return this.cmmUseService.backMsg(model, "게시물 삭제권한이 없습니다.");
        }
        String explainCnt = "";
        if (chkid != null && chkid.length > 0) {
            int processed = 0;
            int todo = chkid.length;
            for (int i = chkid.length - 1; i >= 0; i--) {
                bbsNttVO.setNttNo(chkid[i].intValue());
                if (this.bbsNttService.selectBbsNttLwprtCnt(bbsNttVO) == 0) {
                    this.bbsNttService.deleteBbsNtt(request, chkid[i].intValue());
                    processed++;
                }
            }
            explainCnt = "총 " + todo + "건 중 " + processed + "건에 대해 ";
        }
        return this.cmmUseService.redirectMsg(model, String.valueOf(explainCnt) + "DB삭제를 완료하였습니다.", "/" + siteId + "/selectBbsNttList.do?bbsNo=" + bbsNttVO.getBbsNo() + BeanFactory.FACTORY_BEAN_PREFIX + bbsNttVO.getQs());
    }
}
